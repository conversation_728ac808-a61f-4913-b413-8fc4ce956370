package jp.co.cac.ope.shokken.lab.ui.interview

import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.widget.Button
import androidx.appcompat.app.AppCompatActivity
import jp.co.cac.ope.shokken.lab.R

/**
 * This activity provides buttons to directly launch fragments for testing.
 * It's a simpler approach than using the test framework.
 */
class DirectFragmentLauncherActivity : AppCompatActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_fragment_launcher)

        Log.d("DirectLauncher", "Activity created")

        // Set up button click listeners
        findViewById<Button>(R.id.btn_launch_interview_result).setOnClickListener {
            Log.d("DirectLauncher", "Launch InterviewResultInterviewFragment button clicked")
            launchFragment("RESULT")
        }

        findViewById<Button>(R.id.btn_launch_interview_record_detail).setOnClickListener {
            Log.d("DirectLauncher", "Launch InterviewRecordDetailFragment button clicked")
            launchFragment("RECORD")
        }

        findViewById<Button>(R.id.btn_launch_create_new_user_input).setOnClickListener {
            Log.d("DirectLauncher", "Launch CreateNewUserInputFragment button clicked")
            launchFragment("CREATE_USER")
        }

        findViewById<Button>(R.id.btn_launch_actual_interview_start).setOnClickListener {
            Log.d("DirectLauncher", "Launch ActualInterviewStartFragment button clicked")
            launchFragment("ACTUAL_INTERVIEW_START")
        }

        findViewById<Button>(R.id.btn_launch_actual_interview_result).setOnClickListener {
            Log.d("DirectLauncher", "Launch ActualInterviewResultFragment button clicked")
            launchFragment("ACTUAL_INTERVIEW_RESULT")
        }

        findViewById<Button>(R.id.btn_launch_actual_interview_record_detail).setOnClickListener {
            Log.d("DirectLauncher", "Launch ActualInterviewRecordDetailFragment button clicked")
            launchFragment("ACTUAL_INTERVIEW_RECORD_DETAIL")
        }
    }

    private fun launchFragment(fragmentType: String) {
        try {
            Log.d("DirectLauncher", "Launching fragment: $fragmentType")
            val intent = Intent(this, FragmentTestActivity::class.java).apply {
                putExtra("FRAGMENT_TYPE", fragmentType)
            }
            startActivity(intent)
        } catch (e: Exception) {
            Log.e("DirectLauncher", "Error launching fragment: ${e.message}")
            e.printStackTrace()
        }
    }
}
