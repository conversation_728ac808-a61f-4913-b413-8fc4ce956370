package jp.co.cac.ope.shokken.lab.ui.interview

import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.FrameLayout
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.NavController
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.ui.setupWithNavController
import com.google.android.material.bottomnavigation.BottomNavigationView
import jp.co.cac.ope.shokken.lab.AppConstants
import jp.co.cac.ope.shokken.lab.R
import jp.co.cac.ope.shokken.lab.model.ActualInterviewContentModel
import jp.co.cac.ope.shokken.lab.model.ActualInterviewGradeInfoModel
import jp.co.cac.ope.shokken.lab.model.ActualInterviewTranscriptionModel
import jp.co.cac.ope.shokken.lab.model.ExpressionAnalysisInfoModel
import jp.co.cac.ope.shokken.lab.model.MockInterviewContentModel
import jp.co.cac.ope.shokken.lab.model.MockInterviewGradeInfoModel
import jp.co.cac.ope.shokken.lab.model.MockInterviewTranscriptionModel
import jp.co.cac.ope.shokken.lab.model.ScorePerFrameModel
import jp.co.cac.ope.shokken.lab.ui.actual_interview.ActualInterviewRecordDetailFragment
import jp.co.cac.ope.shokken.lab.ui.actual_interview.ActualInterviewRecordViewModel
import jp.co.cac.ope.shokken.lab.ui.actual_interview.ActualInterviewResultFragment
import jp.co.cac.ope.shokken.lab.ui.actual_interview.ActualInterviewStartFragment
import jp.co.cac.ope.shokken.lab.ui.actual_interview.ActualInterviewViewModel
import jp.co.cac.ope.shokken.lab.ui.actual_interview.CompletedInterview
import jp.co.cac.ope.shokken.lab.ui.createnewuser.CreateNewUserInputFragment
import jp.co.cac.ope.shokken.lab.ui.createnewuser.CreateNewUserInputViewModel
import jp.co.cac.ope.shokken.lab.ui.createnewuser.CreateNewUserViewModel
import java.io.File

/**
 * This activity is designed specifically for testing fragments.
 * It provides a simplified environment that mimics the HomeActivity
 * but with direct fragment loading and mock data setup.
 */
class FragmentTestActivity : AppCompatActivity() {

    private lateinit var container: FrameLayout
    private lateinit var navView: BottomNavigationView
    private lateinit var navController: NavController

    // ViewModels
    private lateinit var interviewViewModel: InterviewViewModel
    private lateinit var interviewRecordViewModel: InterviewRecordViewModel
    private lateinit var createNewUserViewModel: CreateNewUserViewModel
    private lateinit var createNewUserInputViewModel: CreateNewUserInputViewModel
    private lateinit var actualInterviewViewModel: ActualInterviewViewModel

    // Arguments for fragments
    private var actualInterviewRecordDetailArgs: Bundle? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        setTheme(R.style.Theme_OPELab)
        super.onCreate(savedInstanceState)

        // Set the content view to a simple layout with a container and bottom nav
        setContentView(R.layout.activity_fragment_test)

        // Get references to views
        container = findViewById(R.id.fragment_container)
        navView = findViewById(R.id.nav_view)
        findViewById<View>(R.id.close_btn)?.setOnClickListener {
            finish()
        }

        // Set up the NavController with the NavHostFragment
        val navHostFragment =
            supportFragmentManager.findFragmentById(R.id.nav_host_fragment) as NavHostFragment
        navController = navHostFragment.navController

        // Set up the bottom navigation with the NavController
        navView.setupWithNavController(navController)

        try {
            // Initialize ViewModels
            interviewViewModel = ViewModelProvider(this)[InterviewViewModel::class.java]
            interviewRecordViewModel = ViewModelProvider(this)[InterviewRecordViewModel::class.java]
            createNewUserViewModel = ViewModelProvider(this)[CreateNewUserViewModel::class.java]
            createNewUserInputViewModel =
                ViewModelProvider(this)[CreateNewUserInputViewModel::class.java]
            actualInterviewViewModel = ViewModelProvider(this)[ActualInterviewViewModel::class.java]

            // Setup mock data
            setupMockData()

            // Get the fragment type from the intent
            val fragmentType = intent.getStringExtra("FRAGMENT_TYPE") ?: "RESULT"

            // Launch the appropriate fragment
            when (fragmentType) {
                "RESULT" -> launchFragment(InterviewResultInterviewFragment())
                "RECORD" -> launchFragment(InterviewRecordDetailFragment())
                "CREATE_USER" -> {
                    // Setup mock data for CreateNewUserInputFragment
                    setupCreateNewUserMockData()
                    launchFragment(CreateNewUserInputFragment())
                }

                "ACTUAL_INTERVIEW_START" -> {
                    // Setup mock data for ActualInterviewStartFragment
                    launchFragment(ActualInterviewStartFragment())
                }

                "ACTUAL_INTERVIEW_RESULT" -> {
                    // Setup mock data for ActualInterviewResultFragment
                    setupActualInterviewResultMockData()
                    launchFragment(ActualInterviewResultFragment())
                }
                "ACTUAL_INTERVIEW_RECORD_DETAIL" -> {
                    // Setup mock data for ActualInterviewRecordDetailFragment
                    setupActualInterviewRecordDetailMockData()
                    launchFragment(ActualInterviewRecordDetailFragment())
                }
            }
        } catch (e: Exception) {
            Log.e("FragmentTestActivity", "Error in onCreate: ${e.message}")
            e.printStackTrace()
        }
    }

    private fun launchFragment(fragment: Fragment) {
        try {
            // For fragments that are part of the navigation graph, we don't need to manually add them
            // The NavController will handle it based on the navigation graph
            // This is only used for fragments that are not part of the navigation graph
            if (fragment !is CreateNewUserInputFragment) {
                // If it's the ActualInterviewRecordDetailFragment, we need to set the arguments
                if (fragment is ActualInterviewRecordDetailFragment) {
                    // Set the arguments that were created in setupActualInterviewRecordDetailMockData
                    actualInterviewRecordDetailArgs?.let { args ->
                        fragment.arguments = args
                    }
                }

                supportFragmentManager.beginTransaction()
                    .replace(R.id.fragment_container, fragment)
                    .commit()

                // Hide the NavHostFragment when showing a non-navigation fragment
                findViewById<View>(R.id.nav_host_fragment).visibility = View.GONE
                container.visibility = View.VISIBLE
            } else {
                // For navigation fragments, make sure the NavHostFragment is visible
                findViewById<View>(R.id.nav_host_fragment).visibility = View.VISIBLE
                container.visibility = View.GONE

                // If it's the CreateNewUserInputFragment, navigate to it using the NavController
                if (fragment is CreateNewUserInputFragment) {
                    // The navigation graph already has this as the start destination
                    Log.d(
                        "FragmentTestActivity",
                        "Using NavController for CreateNewUserInputFragment"
                    )
                }
            }
        } catch (e: Exception) {
            Log.e("FragmentTestActivity", "Error launching fragment: ${e.message}")
            e.printStackTrace()
        }
    }

    private fun setupMockData() {
        try {
            // Setup InterviewViewModel mock data
            interviewViewModel.selectInterview = MockInterviewContentModel().apply {
                mock_interview_contents_id = 1
                mock_interview_contents_name = "模擬面接テスト"
                mock_interview_contents_code = "TEST_CODE"
                practice_index_id = 1
                practice_index_name = "テスト練習"
                estimated_time = "00:05:00"
                script_hint_new = "これはテストの台本です"
                script_hint_change = "これは変更されたテストの台本です"
            }

            interviewViewModel.mockInterviewGradeInfo = MockInterviewGradeInfoModel().apply {
                mock_interview_grade_id = 1
                user_id = 1
                mock_interview_contents_id = 1
                total_score = 85
                scored_datetime = "2023-10-01 12:00:00"
                memo = "テストメモ"
                movie_file_name = "test_movie.mp4"

                // Create mock expression analysis
                expression_analysis = arrayListOf<ExpressionAnalysisInfoModel>().apply {
                    add(ExpressionAnalysisInfoModel().apply {
                        analysis_item_id = 1
                        analysis_item_name = "表情"
                        analysis_score = 80
                        analysis_description = "表情の分析結果です"
                        order = 1
                    })
                    add(ExpressionAnalysisInfoModel().apply {
                        analysis_item_id = 2
                        analysis_item_name = "姿勢"
                        analysis_score = 90
                        analysis_description = "姿勢の分析結果です"
                        order = 2
                    })
                    add(ExpressionAnalysisInfoModel().apply {
                        analysis_item_id = 3
                        analysis_item_name = "視線"
                        analysis_score = 85
                        analysis_description = "視線の分析結果です"
                        order = 3
                    })
                    add(ExpressionAnalysisInfoModel().apply {
                        analysis_item_id = 4
                        analysis_item_name = "声の大きさ"
                        analysis_score = 75
                        analysis_description = "声の大きさの分析結果です"
                        order = 4
                    })
                }

                // Add mock score_per_frame data
                score_per_frame = arrayListOf<ScorePerFrameModel>().apply {
                    // Using the format from the JSON example with negative score_minus values
                    add(ScorePerFrameModel(100f, -21.08f, "00:00:03.119"))
                    add(ScorePerFrameModel(100f, -32.29f, "00:00:03.423"))
                    add(ScorePerFrameModel(100f, -29.6f, "00:00:03.672"))
                    add(ScorePerFrameModel(100f, -28.66f, "00:00:04.018"))
                    add(ScorePerFrameModel(100f, -28.27f, "00:00:04.468"))
                    add(ScorePerFrameModel(100f, -26.6f, "00:00:04.947"))
                    add(ScorePerFrameModel(100f, -23.81f, "00:00:05.462"))
                    add(ScorePerFrameModel(100f, -29.34f, "00:00:06.040"))
                    add(ScorePerFrameModel(100f, -30.57f, "00:00:06.667"))
                    add(ScorePerFrameModel(100f, -4.82f, "00:00:07.358"))
                    add(ScorePerFrameModel(100f, -26.58f, "00:00:08.046"))
                    add(ScorePerFrameModel(100f, -35.76f, "00:00:08.770"))
                    add(ScorePerFrameModel(100f, -28.18f, "00:00:09.319"))
                    add(ScorePerFrameModel(100f, -25.27f, "00:00:09.885"))
                    add(ScorePerFrameModel(100f, -25.33f, "00:00:10.294"))
                    add(ScorePerFrameModel(100f, -25.35f, "00:00:10.794"))
                    add(ScorePerFrameModel(100f, -25.1f, "00:00:11.293"))
                    add(ScorePerFrameModel(100f, -25.11f, "00:00:11.874"))
                    add(ScorePerFrameModel(100f, -25.07f, "00:00:12.377"))
                    add(ScorePerFrameModel(100f, -24.88f, "00:00:13.291"))
                }
            }

            interviewViewModel.mockInterviewTranscription =
                MockInterviewTranscriptionModel().apply {
                    script_similarity = 85
                    talk_speed = 250
                    transcription = "これはテストの文字起こしです。模擬面接の内容をここに表示します。"
                    talk_speed_appropriate_max = 330
                    talk_speed_appropriate_min = 200
                }

            // Setup InterviewRecordViewModel mock data
            interviewRecordViewModel.selectInterview = MockInterviewContentModel().apply {
                mock_interview_contents_id = 1
                mock_interview_contents_name = "過去の模擬面接テスト"
                mock_interview_contents_code = "PAST_TEST_CODE"
                practice_index_id = 1
                practice_index_name = "過去のテスト練習"
                estimated_time = "00:05:00"
                script_hint_new = "これは過去のテストの台本です"
                script_hint_change = "これは変更された過去のテストの台本です"
            }

            interviewRecordViewModel.mockInterviewGradeInfo = MockInterviewGradeInfoModel().apply {
                mock_interview_grade_id = 2
                user_id = 1
                mock_interview_contents_id = 1
                total_score = 90
                scored_datetime = "2023-09-15 15:30:00"
                memo = "過去のテストメモ"
                movie_file_name = "past_test_movie.mp4"

                // Create mock expression analysis
                expression_analysis = arrayListOf<ExpressionAnalysisInfoModel>().apply {
                    add(ExpressionAnalysisInfoModel().apply {
                        analysis_item_id = 1
                        analysis_item_name = "表情"
                        analysis_score = 85
                        analysis_description = "過去の表情の分析結果です"
                        order = 1
                    })
                    add(ExpressionAnalysisInfoModel().apply {
                        analysis_item_id = 2
                        analysis_item_name = "姿勢"
                        analysis_score = 95
                        analysis_description = "過去の姿勢の分析結果です"
                        order = 2
                    })
                    add(ExpressionAnalysisInfoModel().apply {
                        analysis_item_id = 3
                        analysis_item_name = "視線"
                        analysis_score = 90
                        analysis_description = "過去の視線の分析結果です"
                        order = 3
                    })
                    add(ExpressionAnalysisInfoModel().apply {
                        analysis_item_id = 4
                        analysis_item_name = "声の大きさ"
                        analysis_score = 88
                        analysis_description = "過去の声の大きさの分析結果です"
                        order = 4
                    })
                }
            }

            interviewRecordViewModel.mockInterviewTranscription =
                MockInterviewTranscriptionModel().apply {
                    script_similarity = 90
                    talk_speed = 270
                    transcription =
                        "これは過去のテストの文字起こしです。過去の模擬面接の内容をここに表示します。"
                    talk_speed_appropriate_max = 330
                    talk_speed_appropriate_min = 200
                }

            Log.d("FragmentTestActivity", "Mock data setup complete")
        } catch (e: Exception) {
            Log.e("FragmentTestActivity", "Error setting up mock data: ${e.message}")
            e.printStackTrace()
        }
    }

    // Methods to mimic HomeActivity functionality
    fun hideBottomNavigationView() {
        navView.visibility = View.GONE
    }

    fun showBottomNavigationView() {
        navView.visibility = View.VISIBLE
    }

    // Handle navigation with the real NavController

    // Override the back button to handle navigation properly
    override fun onBackPressed() {
        // If we can pop the back stack, do that
        if (navController.currentDestination?.id != navController.graph.startDestinationId) {
            navController.popBackStack()
        } else {
            // Otherwise, let the system handle it
            super.onBackPressed()
        }
    }

    // This method can be called from fragments when they try to navigate
    fun handleNavigation(destinationId: Int, args: Bundle? = null) {
        Log.d("FragmentTestActivity", "Navigation requested to destination: $destinationId")
        navController.navigate(destinationId, args)
    }

    // Method to get the NavController for fragments that need it
    fun getNavController(): NavController {
        return navController
    }

    // Setup mock data for CreateNewUserInputFragment
    private fun setupCreateNewUserMockData() {
        try {
            // Pre-fill the CreateNewUserViewModel with mock data
            createNewUserViewModel.apply {
                email = "<EMAIL>"
                confirmEmail = "<EMAIL>"
                password = "Password123!"
                confirmPassword = "Password123!"
                mode = 2
            }

            // Set up the gender and graduation type buttons in the ViewModel
            createNewUserInputViewModel.apply {
                onGraduationButtonClick() // Set graduation type to graduated
            }

            Log.d("FragmentTestActivity", "CreateNewUser mock data setup complete")
        } catch (e: Exception) {
            Log.e("FragmentTestActivity", "Error setting up CreateNewUser mock data: ${e.message}")
            e.printStackTrace()
        }
    }

    // Setup mock data for ActualInterviewRecordDetailFragment
    private fun setupActualInterviewRecordDetailMockData() {
        try {
            // Setup mock data for ActualInterviewRecordDetailFragment
            val actualInterviewRecordViewModel = ViewModelProvider(this)[ActualInterviewRecordViewModel::class.java]

            // Create mock content list if it's empty
            if (actualInterviewRecordViewModel.actualInterviewContentList.isEmpty()) {
                actualInterviewRecordViewModel.actualInterviewContentList = arrayListOf(
                    ActualInterviewContentModel().apply {
                        actual_interview_contents_name = "自己紹介練習"
                        practice_index_id = 1
                        practice_index_name = actual_interview_contents_name
                        estimated_time = "00:02:00"
                        script_hint_new = "私の名前は〇〇です。〇〇大学の〇〇学部に所属しています。"
                        script_hint_change = "私の名前は〇〇です。〇〇大学の〇〇学部に所属しています。趣味は〇〇です。"
                    },
                    ActualInterviewContentModel().apply {
                        actual_interview_contents_name = "志望動機練習"
                        practice_index_id = 2
                        practice_index_name = actual_interview_contents_name
                        estimated_time = "00:03:00"
                        script_hint_new = "貴社を志望した理由は〇〇です。"
                        script_hint_change = "貴社を志望した理由は〇〇です。特に〇〇という点に魅力を感じています。"
                    },
                    ActualInterviewContentModel().apply {
                        actual_interview_contents_name = "長所・短所練習"
                        practice_index_id = 3
                        practice_index_name = actual_interview_contents_name
                        estimated_time = "00:03:00"
                        script_hint_new = "私の長所は〇〇です。短所は〇〇です。"
                        script_hint_change = "私の長所は〇〇です。短所は〇〇ですが、それを克服するために〇〇しています。"
                    }
                )
            }

            // Create mock grade info and transcription data
            val gradeInfo1 = ActualInterviewGradeInfoModel().apply {
                actual_interview_grade_id = 1
                user_id = 1
                total_score = 85
                scored_datetime = "2023-10-01 12:00:00"
                memo = "自己紹介のテストメモ"
                movie_file_name = "interview1.mp4"
                expression_analysis = arrayListOf<ExpressionAnalysisInfoModel>().apply {
                    add(ExpressionAnalysisInfoModel().apply {
                        analysis_item_id = 1
                        analysis_item_name = "表情"
                        analysis_score = 80
                        analysis_description = "表情の分析結果です"
                        order = 1
                    })
                    add(ExpressionAnalysisInfoModel().apply {
                        analysis_item_id = 2
                        analysis_item_name = "姿勢"
                        analysis_score = 90
                        analysis_description = "姿勢の分析結果です"
                        order = 2
                    })
                }

                // Add mock score_per_frame data
                score_per_frame = arrayListOf<ScorePerFrameModel>().apply {
                    add(ScorePerFrameModel(0f, 43f, "00:00:02.312"))
                    add(ScorePerFrameModel(0f, 43f, "00:00:02.515"))
                    add(ScorePerFrameModel(10f, 119f, "00:00:02.789"))
                    add(ScorePerFrameModel(10f, 87f, "00:00:03.734"))
                    add(ScorePerFrameModel(0f, 48f, "00:00:03.740"))
                    add(ScorePerFrameModel(0f, 46f, "00:00:03.748"))
                    add(ScorePerFrameModel(0f, 43f, "00:00:03.876"))
                    add(ScorePerFrameModel(20f, 30f, "00:00:04.000"))
                    add(ScorePerFrameModel(40f, 20f, "00:00:04.500"))
                    add(ScorePerFrameModel(60f, 100f, "00:00:05.000"))
                }
            }

            val transcription1 = ActualInterviewTranscriptionModel().apply {
                script_similarity = 90
                talk_speed = 250
                transcription = "私の名前はテスト太郎です。テスト大学のテスト学部に所属しています。趣味は読書です。"
                talk_speed_appropriate_max = 330
                talk_speed_appropriate_min = 200
            }

            // Add the mock data to the cache map
            actualInterviewRecordViewModel.actualInterviewGradeInfoCacheMap[1] = Pair(gradeInfo1, transcription1)

            // Create a list of grade IDs to pass to the fragment
            val gradeIdList = arrayListOf(1)

            // Store the arguments to be used when creating the fragment
            val args = ActualInterviewRecordDetailFragment.args(gradeIdList)
            actualInterviewRecordDetailArgs = args

            Log.d("FragmentTestActivity", "ActualInterviewRecordDetail mock data setup complete")
        } catch (e: Exception) {
            Log.e("FragmentTestActivity", "Error setting up ActualInterviewRecordDetail mock data: ${e.message}")
            e.printStackTrace()
        }
    }

    // Setup mock data for ActualInterviewResultFragment
    private fun setupActualInterviewResultMockData() {
        try {
            // Setup mock data for ActualInterviewResultFragment
            actualInterviewViewModel.apply {
                // Create a list of mock interviews (same as in setupActualInterviewMockData)
                val interviewList = arrayListOf<ActualInterviewContentModel>()

                // Add first interview
                interviewList.add(ActualInterviewContentModel().apply {
                    actual_interview_contents_name = "自己紹介練習"
                    practice_index_id = 1
                    practice_index_name = actual_interview_contents_name
                    estimated_time = "00:02:00"
                    script_hint_new = "私の名前は〇〇です。〇〇大学の〇〇学部に所属しています。"
                    script_hint_change =
                        "私の名前は〇〇です。〇〇大学の〇〇学部に所属しています。趣味は〇〇です。"
                })

                // Add second interview
                interviewList.add(ActualInterviewContentModel().apply {
                    actual_interview_contents_name = "志望動機練習"
                    practice_index_id = 2
                    practice_index_name = actual_interview_contents_name
                    estimated_time = "00:03:00"
                    script_hint_new = "貴社を志望した理由は〇〇です。"
                    script_hint_change =
                        "貴社を志望した理由は〇〇です。特に〇〇という点に魅力を感じています。"
                })

                // Add third interview
                interviewList.add(ActualInterviewContentModel().apply {
                    actual_interview_contents_name = "長所・短所練習"
                    practice_index_id = 3
                    practice_index_name = actual_interview_contents_name
                    estimated_time = "00:03:00"
                    script_hint_new = "私の長所は〇〇です。短所は〇〇です。"
                    script_hint_change =
                        "私の長所は〇〇です。短所は〇〇ですが、それを克服するために〇〇しています。"
                })

                // Create mock completed interviews
                completedInterviews = mutableListOf(
                    CompletedInterview(
                        interview = interviewList[0],
                        outputFile = File(
                            getDir(AppConstants.MOVIE_DIR_NAME, MODE_PRIVATE),
                            "interview1.mp4"
                        ),
                        faceDetectData = arrayListOf(mutableMapOf("joy" to 0.8, "anger" to 0.1)),
                        transcription = "私の名前はテスト太郎です。テスト大学のテスト学部に所属しています。趣味は読書です。",
                        speechDuration = 15000 // 15 seconds
                    ),
                    CompletedInterview(
                        interview = interviewList[1],
                        outputFile = File(
                            getDir(AppConstants.MOVIE_DIR_NAME, MODE_PRIVATE),
                            "interview2.mp4"
                        ),
                        faceDetectData = arrayListOf(mutableMapOf("joy" to 0.7, "anger" to 0.2)),
                        transcription = "貴社を志望した理由は御社の革新的な技術開発に魅力を感じたからです。特にAI技術の応用分野に興味があります。",
                        speechDuration = 20000 // 20 seconds
                    ),
                    CompletedInterview(
                        interview = interviewList[2],
                        outputFile = File(
                            getDir(AppConstants.MOVIE_DIR_NAME, MODE_PRIVATE),
                            "interview3.mp4"
                        ),
                        faceDetectData = arrayListOf(mutableMapOf("joy" to 0.6, "anger" to 0.3)),
                        transcription = "私の長所は粘り強さです。短所は緊張しやすいことですが、事前準備を徹底することで克服しています。",
                        speechDuration = 25000 // 25 seconds
                    )
                )

                // Create mock grade info for each interview
                actualInterviewGradeInfoList = mutableListOf(
                    ActualInterviewGradeInfoModel().apply {
                        user_id = 1
                        total_score = 85
                        scored_datetime = "2023-10-01 12:00:00"
                        memo = "自己紹介のテストメモ"
                        movie_file_name = "interview1.mp4"
                        expression_analysis = arrayListOf<ExpressionAnalysisInfoModel>().apply {
                            add(ExpressionAnalysisInfoModel().apply {
                                analysis_item_id = 1
                                analysis_item_name = "表情"
                                analysis_score = 80
                                analysis_description = "表情の分析結果です"
                                order = 1
                            })
                            add(ExpressionAnalysisInfoModel().apply {
                                analysis_item_id = 2
                                analysis_item_name = "姿勢"
                                analysis_score = 90
                                analysis_description = "姿勢の分析結果です"
                                order = 2
                            })
                            add(ExpressionAnalysisInfoModel().apply {
                                analysis_item_id = 3
                                analysis_item_name = "Custom 3"
                                analysis_score = 30
                                analysis_description = "Desc 3"
                                order = 3
                            })
                            add(ExpressionAnalysisInfoModel().apply {
                                analysis_item_id = 4
                                analysis_item_name = "Custom 4"
                                analysis_score = 40
                                analysis_description = "Desc 4"
                                order = 4
                            })
                        }

                        // Add mock score_per_frame data for the first interview
                        score_per_frame = arrayListOf<ScorePerFrameModel>().apply {
                            // Using the format from the JSON example with negative score_minus values
                            add(ScorePerFrameModel(100f, -21.08f, "00:00:03.119"))
                            add(ScorePerFrameModel(100f, -32.29f, "00:00:03.423"))
                            add(ScorePerFrameModel(100f, -29.6f, "00:00:03.672"))
                            add(ScorePerFrameModel(100f, -28.66f, "00:00:04.018"))
                            add(ScorePerFrameModel(100f, -28.27f, "00:00:04.468"))
                            add(ScorePerFrameModel(100f, -26.6f, "00:00:04.947"))
                            add(ScorePerFrameModel(100f, -23.81f, "00:00:05.462"))
                            add(ScorePerFrameModel(100f, -29.34f, "00:00:06.040"))
                            add(ScorePerFrameModel(100f, -30.57f, "00:00:06.667"))
                            add(ScorePerFrameModel(100f, -4.82f, "00:00:07.358"))
                        }
                    },
                    ActualInterviewGradeInfoModel().apply {
                        user_id = 1
                        total_score = 88
                        scored_datetime = "2023-10-01 12:05:00"
                        memo = "志望動機のテストメモ"
                        movie_file_name = "interview2.mp4"
                        expression_analysis = arrayListOf<ExpressionAnalysisInfoModel>().apply {
                            add(ExpressionAnalysisInfoModel().apply {
                                analysis_item_id = 1
                                analysis_item_name = "表情"
                                analysis_score = 85
                                analysis_description = "表情の分析結果です"
                                order = 1
                            })
                            add(ExpressionAnalysisInfoModel().apply {
                                analysis_item_id = 2
                                analysis_item_name = "姿勢"
                                analysis_score = 92
                                analysis_description = "姿勢の分析結果です"
                                order = 2
                            })
                            add(ExpressionAnalysisInfoModel().apply {
                                analysis_item_id = 3
                                analysis_item_name = "Custom 3"
                                analysis_score = 55
                                analysis_description = "Desc 3"
                                order = 3
                            })
                            add(ExpressionAnalysisInfoModel().apply {
                                analysis_item_id = 4
                                analysis_item_name = "Custom 4"
                                analysis_score = 65
                                analysis_description = "Desc 4"
                                order = 4
                            })
                        }

                        // Add mock score_per_frame data for the second interview
                        score_per_frame = arrayListOf<ScorePerFrameModel>().apply {
                            // Using the format from the JSON example with negative score_minus values
                            add(ScorePerFrameModel(80f, -35.76f, "00:00:08.770"))
                            add(ScorePerFrameModel(85f, -28.18f, "00:00:09.319"))
                            add(ScorePerFrameModel(90f, -25.27f, "00:00:09.885"))
                            add(ScorePerFrameModel(95f, -25.33f, "00:00:10.294"))
                            add(ScorePerFrameModel(100f, -25.35f, "00:00:10.794"))
                            add(ScorePerFrameModel(100f, -25.1f, "00:00:11.293"))
                            add(ScorePerFrameModel(100f, -25.11f, "00:00:11.874"))
                            add(ScorePerFrameModel(100f, -25.07f, "00:00:12.377"))
                            add(ScorePerFrameModel(100f, -25.07f, "00:00:12.828"))
                            add(ScorePerFrameModel(100f, -24.88f, "00:00:13.291"))
                        }
                    },
                    ActualInterviewGradeInfoModel().apply {
                        user_id = 1
                        total_score = 82
                        scored_datetime = "2023-10-01 12:10:00"
                        memo = "長所・短所のテストメモ"
                        movie_file_name = "interview3.mp4"
                        expression_analysis = arrayListOf<ExpressionAnalysisInfoModel>().apply {
                            add(ExpressionAnalysisInfoModel().apply {
                                analysis_item_id = 1
                                analysis_item_name = "表情"
                                analysis_score = 78
                                analysis_description = "表情の分析結果です"
                                order = 1
                            })
                            add(ExpressionAnalysisInfoModel().apply {
                                analysis_item_id = 2
                                analysis_item_name = "姿勢"
                                analysis_score = 86
                                analysis_description = "姿勢の分析結果です"
                                order = 2
                            })
                            add(ExpressionAnalysisInfoModel().apply {
                                analysis_item_id = 3
                                analysis_item_name = "Custom 3"
                                analysis_score = 99
                                analysis_description = "Desc 3"
                                order = 3
                            })
                            add(ExpressionAnalysisInfoModel().apply {
                                analysis_item_id = 4
                                analysis_item_name = "Custom 4"
                                analysis_score = 56
                                analysis_description = "Desc 4"
                                order = 4
                            })
                        }

                        // Add mock score_per_frame data for the third interview
                        score_per_frame = arrayListOf<ScorePerFrameModel>().apply {
                            // Using the format from the JSON example with negative score_minus values
                            add(ScorePerFrameModel(100f, -24.88f, "00:00:13.966"))
                            add(ScorePerFrameModel(100f, -24.85f, "00:00:14.397"))
                            add(ScorePerFrameModel(100f, -24.74f, "00:00:14.979"))
                            add(ScorePerFrameModel(100f, -24.71f, "00:00:15.476"))
                            add(ScorePerFrameModel(100f, -24.71f, "00:00:16.036"))
                            add(ScorePerFrameModel(100f, -24.6f, "00:00:16.534"))
                            add(ScorePerFrameModel(100f, -24.6f, "00:00:17.022"))
                            add(ScorePerFrameModel(100f, -24.58f, "00:00:17.491"))
                            add(ScorePerFrameModel(100f, -24.58f, "00:00:18.001"))
                            add(ScorePerFrameModel(100f, -24.5f, "00:00:18.517"))
                        }
                    }
                )
                saveActualInterviewGradeInfoList = ArrayList(actualInterviewGradeInfoList)

                // Set up the current interview's transcription
                transcriptionList = mutableListOf(
                    ActualInterviewTranscriptionModel().apply {
                        script_similarity = 90
                        talk_speed = 250
                        transcription =
                            "私の名前はテスト太郎です。テスト大学のテスト学部に所属しています。趣味は読書です。"
                        talk_speed_appropriate_max = 330
                        talk_speed_appropriate_min = 200
                    }
                )
            }
        } catch (e: Exception) {
            Log.e(
                "FragmentTestActivity",
                "Error setting up ActualInterviewResult mock data: ${e.message}"
            )
            e.printStackTrace()
        }
    }
}
