<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.interview.DirectFragmentLauncherActivity">

    <LinearLayout
        android:id="@+id/button_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="24dp"
            android:gravity="center"
            android:text="Fragment Test Launcher"
            android:textSize="24sp"
            android:textStyle="bold" />

        <Button
            android:id="@+id/btn_launch_interview_result"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:text="Launch InterviewResultInterviewFragment" />

        <Button
            android:id="@+id/btn_launch_interview_record_detail"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:text="Launch InterviewRecordDetailFragment" />

        <Button
            android:id="@+id/btn_launch_create_new_user_input"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:text="Launch CreateNewUserInputFragment (Autofill)" />

        <Button
            android:id="@+id/btn_launch_actual_interview_start"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:text="Launch ActualInterviewStartFragment" />

        <Button
            android:id="@+id/btn_launch_actual_interview_result"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:text="Launch ActualInterviewResultFragment" />

        <Button
            android:id="@+id/btn_launch_actual_interview_record_detail"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:text="Launch ActualInterviewRecordDetailFragment" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:gravity="center"
            android:text="Note: This is a debug-only activity for testing fragments in isolation."
            android:textStyle="italic" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
