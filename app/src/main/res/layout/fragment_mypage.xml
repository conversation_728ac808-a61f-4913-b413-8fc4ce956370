<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.mypage.MyPageFragment">

    <include
        android:id="@+id/mainPageHeaderBar"
        layout="@layout/header_bar_main_page"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    <ScrollView
        android:id="@+id/selectView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="48dp"
        android:background="@color/gray_bg01"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/mainPageHeaderBar">
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/base_white"
            android:orientation="vertical"
            android:gravity="center_horizontal"
            tools:ignore="MissingConstraints">

            <include
                android:id="@+id/partMypageName"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                layout="@layout/part_mypage_name"
                android:layout_marginLeft="20dp"
                android:layout_marginTop="40dp"
                android:layout_marginRight="20dp"
                android:visibility="visible" />

            <include
                android:id="@+id/partMypageLoginNot"
                layout="@layout/part_mypage_login_not"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="40dp"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:visibility="visible" />

            <include
                android:id="@+id/partMypageLoginFree"
                layout="@layout/part_mypage_login_free"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:visibility="visible" />

            <include
                android:id="@+id/partMypageLoginTral"
                layout="@layout/part_mypage_login_tral"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="32dp"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:visibility="visible" />

            <!--<View
                android:id="@+id/campaignMargin"
                android:layout_width="match_parent"
                android:layout_height="24dp"
                android:visibility="gone"/>-->

            <include
                android:id="@+id/partMypageLoginCurrentPlan"
                layout="@layout/part_mypage_login_current_plan"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:visibility="visible" />

            <!--<ImageView
                android:id="@+id/imgCampaignBg"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="12dp"
                android:src="@drawable/img_campaign_bg" />-->

            <TextView
                android:id="@+id/planChangeMessageView"
                android:layout_width="216dp"
                android:layout_height="wrap_content"
                android:gravity="center_horizontal"
                android:layout_marginTop="8dp"
                android:text="利用中プランの次回更新日時経過後より 1ヶ月プランが適用されます。"
                android:textColor="@color/base_text"
                android:textStyle="normal"
                android:textSize="12sp"/>

            <include
                android:id="@+id/partMypageLoginPlanChange"
                layout="@layout/part_mypage_login_plan_change"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:visibility="visible" />

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginTop="40dp"
                android:layout_marginStart="20dp"
                android:layout_marginEnd="20dp"
                android:layout_gravity="center"
                android:background="#EEEEEE" />
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_marginStart="20dp"
                android:layout_marginEnd="20dp"
                android:layout_height="match_parent">
                <Button
                    android:id="@+id/accountConfirmButton"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:layout_marginTop="0dp"
                    android:background="@color/base_white"
                    android:textColor="@color/base_text"
                    android:gravity="left|center_vertical"
                    android:padding="0dp"
                    android:stateListAnimator="@null"
                    android:text="アカウント情報確認・変更"
                    android:textSize="14sp"
                    android:textStyle="normal"
                    app:backgroundTint="@null"
                    app:elevation="0dp"
                    tools:ignore="MissingConstraints" />
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_arrow_next_base_16dp"
                    android:layout_marginRight="-4dp"
                    tools:ignore="MissingConstraints"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <!--<View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:layout_marginStart="20dp"
                android:layout_marginEnd="20dp"
                android:layout_gravity="center"
                android:background="#EEEEEE" />
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_marginStart="20dp"
                android:layout_marginEnd="20dp"
                android:layout_height="match_parent">
                <Button
                    android:id="@+id/scheduleListButton"
                    android:layout_width="match_parent"
                    android:layout_height="48dp"
                    android:layout_marginTop="0dp"
                    android:background="@color/base_white"
                    android:textColor="@color/base_text"
                    android:gravity="left|center_vertical"
                    android:padding="0dp"
                    android:stateListAnimator="@null"
                    android:text="予定リスト"
                    android:textSize="14sp"
                    android:textStyle="normal"
                    app:backgroundTint="@null"
                    app:elevation="0dp"
                    tools:ignore="MissingConstraints" />
                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_arrow_next_base_16dp"
                    android:layout_marginRight="-4dp"
                    tools:ignore="MissingConstraints"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>-->

        </LinearLayout>
    </ScrollView>

</androidx.constraintlayout.widget.ConstraintLayout>