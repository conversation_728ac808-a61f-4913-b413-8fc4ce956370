package jp.co.cac.ope.shokken.lab.ui.customview

import android.content.Context
import android.content.res.Resources
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Path
import android.util.AttributeSet
import android.view.View
import androidx.core.content.ContextCompat
import androidx.core.graphics.toColorInt
import jp.co.cac.ope.shokken.lab.R
import jp.co.cac.ope.shokken.lab.model.ScorePerFrameModel
import kotlin.math.abs

class ExpressionGraphView(context: Context, attrs: AttributeSet? = null) : View(context, attrs) {
    // Data for the graph
    private var positiveData: List<Float> = emptyList()
    private var negativeData: List<Float> = emptyList()

    // Colors for the graph
    private var positiveColor = "#DDA1A4".toColorInt()
    private var negativeColor = "#BDC4A5".toColorInt()
    private var gridColor = ContextCompat.getColor(context, R.color.gray_grayout)

    // Paint objects
    private val gridPaint = Paint().apply {
        isAntiAlias = true
        color = gridColor
        style = Paint.Style.STROKE
        strokeWidth = 1.5f * Resources.getSystem().displayMetrics.density
    }

    private val positivePaint = Paint().apply {
        isAntiAlias = true
        color = positiveColor
        style = Paint.Style.FILL
    }

    private val negativePaint = Paint().apply {
        isAntiAlias = true
        color = negativeColor
        style = Paint.Style.FILL
    }

    private val separatorPaint = Paint(gridPaint).apply {
        strokeWidth = 2f * Resources.getSystem().displayMetrics.density
    }

    init {
        setBackgroundColor(Color.TRANSPARENT)
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        val width = width.toFloat()
        val height = height.toFloat()

        val graphMarginLeft = 0f
        val graphMarginTop = 0f
        val graphMarginRight = 0f
        val graphMarginBottom = 0f

        val graphWidth = width - graphMarginLeft - graphMarginRight
        val graphHeight = height - graphMarginTop - graphMarginBottom
        val graphMiddle = graphMarginTop + graphHeight / 2

        // Draw separator line between Plus Element and Negative Element sections
        canvas.drawLine(0f, graphMiddle, width, graphMiddle, separatorPaint)

        // Draw center lines for each section
        val positiveMiddle = graphMarginTop + graphHeight / 4
        val negativeMiddle = graphMiddle + graphHeight / 4
        canvas.drawLine(
            graphMarginLeft,
            positiveMiddle,
            width - graphMarginRight,
            positiveMiddle,
            gridPaint
        )
        canvas.drawLine(
            graphMarginLeft,
            negativeMiddle,
            width - graphMarginRight,
            negativeMiddle,
            gridPaint
        )

        // Draw positive data if available
        if (positiveData.isNotEmpty()) {
            drawSmoothGraph(
                canvas,
                positiveData,
                graphMarginLeft,
                graphMiddle,
                graphWidth,
                graphHeight / 2,
                true
            )
        }

        // Draw negative data if available
        if (negativeData.isNotEmpty()) {
            drawSmoothGraph(
                canvas,
                negativeData,
                graphMarginLeft,
                graphMiddle,
                graphWidth,
                graphHeight / 2,
                false
            )
        }

        // If no data, draw sample data for preview
        /*if (positiveData.isEmpty() && negativeData.isEmpty()) {
            drawSampleData(
                canvas,
                graphMarginLeft,
                graphMarginTop,
                graphWidth,
                graphHeight,
                graphMiddle
            )
        }*/
    }

    private fun drawSmoothGraph(
        canvas: Canvas, data: List<Float>, startX: Float, baselineY: Float,
        width: Float, maxHeight: Float, isPositive: Boolean
    ) {
        if (data.isEmpty()) return

        val path = Path()
        // Calculate segment width to ensure the graph extends to the full width
        val segmentWidth = if (data.size > 1) width / (data.size - 1) else width

        // Start at the baseline
        path.moveTo(startX, baselineY)

        // Add first point at the baseline
        path.lineTo(startX, baselineY)

        // First data point
        if (data[0] > 0) {
            val height = (data[0] * maxHeight).coerceAtMost(maxHeight)
            val y = if (isPositive) baselineY - height else baselineY + height
            path.lineTo(startX, y)
        }

        // Create control points for a smooth curve
        for (i in 1 until data.size) {
            val value = data[i]
            val x = startX + i * segmentWidth
            val height = (value * maxHeight).coerceAtMost(maxHeight)
            val y = if (isPositive) baselineY - height else baselineY + height

            val prevX = startX + (i - 1) * segmentWidth
            val prevValue = data[i - 1]
            val prevHeight = (prevValue * maxHeight).coerceAtMost(maxHeight)
            val prevY = if (isPositive) baselineY - prevHeight else baselineY + prevHeight

            // If either current or previous value is 0, just draw a line
            if (value == 0f || prevValue == 0f) {
                path.lineTo(x, y)
            } else {
                // Calculate control points for a smooth curve
                // Control point 1 is 1/3 of the way from prev to current
                val cp1x = prevX + (x - prevX) / 3
                val cp1y = prevY

                // Control point 2 is 2/3 of the way from prev to current
                val cp2x = prevX + 2 * (x - prevX) / 3
                val cp2y = y

                path.cubicTo(cp1x, cp1y, cp2x, cp2y, x, y)
            }
        }

        // Last data point to baseline - use the full width
        val lastX = startX + width
        path.lineTo(lastX, baselineY)

        // Complete the path by going back to the baseline
        path.lineTo(startX, baselineY)
        path.close()

        // Draw the filled path
        canvas.drawPath(path, if (isPositive) positivePaint else negativePaint)
    }

    /**
     * Draws sample data for preview purposes when no real data is available
     */
    private fun drawSampleData(
        canvas: Canvas, graphMarginLeft: Float, graphMarginTop: Float,
        graphWidth: Float, graphHeight: Float, graphMiddle: Float
    ) {
        // Use fewer sample points to make the graph smoother and ensure it extends to the edges
        val samplePositive = listOf(
            0.1f, 0.05f, 0.02f, 0.8f, 0.05f, 0.02f, 0.01f, 0.9f, 0.1f, 0.05f,
            0.02f, 0.01f, 0.4f, 0.9f, 0.1f, 0.05f, 0.02f, 0.01f, 0.95f, 0.5f
        )
        val sampleNegative = listOf(
            0f, 0f, 0f, 0f, 0f, 0f, 0f, 0f, 0f, 0f,
            0f, 0f, 0f, 0f, 0f, 0.9f, 0.8f, 0.7f, 0.3f, 0.1f
        )

        drawSmoothGraph(
            canvas,
            samplePositive,
            graphMarginLeft,
            graphMiddle,
            graphWidth,
            graphHeight / 2,
            true
        )

        drawSmoothGraph(
            canvas,
            sampleNegative,
            graphMarginLeft,
            graphMiddle,
            graphWidth,
            graphHeight / 2,
            false
        )
    }

    /**
     * Updates the graph with new data
     *
     * @param positiveData List of positive expression values (0.0 to 1.0)
     * @param negativeData List of negative expression values (0.0 to 1.0)
     */
    fun updateData(positiveData: List<Float>, negativeData: List<Float>) {
        this.positiveData = positiveData
        this.negativeData = negativeData
        invalidate() // Redraw the view with new data
    }

    /**
     * Updates the graph with score_per_frame data
     *
     * @param scorePerFrameData List of ScorePerFrameModel objects
     */
    fun updateDataFromScorePerFrame(scorePerFrameData: List<ScorePerFrameModel>?) {
        if (scorePerFrameData == null || scorePerFrameData.isEmpty()) {
            this.positiveData = emptyList()
            this.negativeData = emptyList()
        } else {
            // Extract positive scores directly (already in 0.0 to 1.0 range)
            val positiveScores =
                scorePerFrameData.map { it.score_plus / 100f } // Normalize to 0.0-1.0 range

            // For negative scores, we need to normalize them to 0.0-1.0 range
            // The values come in as negative numbers like -21.08, -32.29, etc.
            // We need to convert them to a 0.0-1.0 scale
            val negativeScores = scorePerFrameData.map {
                // Take absolute value and normalize to 0.0-1.0 range
                // Assuming the max negative value is -100
                abs(it.score_minus) / 100f
            }

            this.positiveData = positiveScores
            this.negativeData = negativeScores
        }
        invalidate() // Redraw the view with new data
    }

    /**
     * Set the color for positive graph elements
     */
    fun setPositiveColor(color: Int) {
        positiveColor = color
        positivePaint.color = color
        invalidate()
    }

    /**
     * Set the color for negative graph elements
     */
    fun setNegativeColor(color: Int) {
        negativeColor = color
        negativePaint.color = color
        invalidate()
    }

    /**
     * Set the color for grid lines
     */
    fun setGridColor(color: Int) {
        gridColor = color
        gridPaint.color = color
        invalidate()
    }
}
