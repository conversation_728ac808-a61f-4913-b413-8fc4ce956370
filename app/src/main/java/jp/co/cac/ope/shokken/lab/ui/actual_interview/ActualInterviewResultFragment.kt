package jp.co.cac.ope.shokken.lab.ui.actual_interview

import android.content.Context.MODE_PRIVATE
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import androidx.activity.OnBackPressedCallback
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import com.google.android.material.bottomnavigation.BottomNavigationView
import jp.co.cac.ope.shokken.lab.AppConstants
import jp.co.cac.ope.shokken.lab.OPELabApplication
import jp.co.cac.ope.shokken.lab.R
import jp.co.cac.ope.shokken.lab.connects.ActualInterviewAPI
import jp.co.cac.ope.shokken.lab.databinding.FragmentActualInterviewResultBinding
import jp.co.cac.ope.shokken.lab.ui.modal.CommonDialogFragment
import jp.co.cac.ope.shokken.lab.utils.CustomerIOActualinterviewUtil
import jp.co.cac.ope.shokken.lab.utils.CustomerIOTrainingUtil
import jp.co.cac.ope.shokken.lab.utils.LoginLogoutUtil
import java.io.File

class ActualInterviewResultFragment : Fragment() {
    private var _binding: FragmentActualInterviewResultBinding? = null

    // This property is only valid between onCreateView and
    // onDestroyView.
    private val binding get() = _binding!!

    private lateinit var actualInterviewViewModel: ActualInterviewViewModel
    private lateinit var actualInterviewResultViewModel: ActualInterviewResultViewModel

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val sharedPreferences = requireActivity().getSharedPreferences(AppConstants.APP_PREF_NAME, MODE_PRIVATE)

        val navView: BottomNavigationView = activity?.findViewById(R.id.nav_view)!!
        navView.visibility = View.GONE

        actualInterviewResultViewModel =
            ViewModelProvider(this)[ActualInterviewResultViewModel::class.java]

        actualInterviewViewModel =
            ViewModelProvider(requireActivity())[ActualInterviewViewModel::class.java]

        _binding = FragmentActualInterviewResultBinding.inflate(inflater, container, false)
        val root: View = binding.root

        binding.headerTitle.text = "採点結果"

        val backButton: ImageButton = binding.closeButton
        backButton.setOnClickListener {
            handleBackPress()
        }

        requireActivity().onBackPressedDispatcher.addCallback(
            viewLifecycleOwner,
            object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {
                    handleBackPress()
                }
            })

        binding.trainingButton.setOnClickListener {
            // CustomerIOへデータ送信
            CustomerIOTrainingUtil.startedTraining()

            handleBackPress(R.id.action_navigation_actual_interview_result_to_navigation_training_top)
        }


        binding.viewPager.apply {
            isUserInputEnabled = false
            adapter = ActualInterviewResultPagerAdapter(
                this@ActualInterviewResultFragment,
                actualInterviewViewModel.actualInterviewGradeInfoList
            )
        }

        binding.saveAnalyze.setOnCheckedChangeListener { _, isChecked ->
            if (isChecked) {
                // Do nothing
            } else {
                CommonDialogFragment.newInstance(
                    "採点結果を保存せず画面を閉じると今回の結果とメモは削除されます。\n保存しないで良いですか？",
                    true,
                    "保存しない",
                    object : CommonDialogFragment.CallbackListener {
                        override fun callbackFromDialogCloseButton() {
                            // Do nothing
                        }
                        override fun callbackFromDialogCancelButton() {
                            binding.saveAnalyze.isChecked = true
                        }
                    },
                    true,
                    "キャンセル"
                ).show(parentFragmentManager, "CommonDialogFragment")
            }
        }

        return root
    }

    private fun registerActualInterviewGradeAndMemo(
        successCallback: () -> Unit,
        failureCallback: () -> Unit
    ) {
        //if(actualInterviewViewModel.saveActualInterviewGradeInfoList.isEmpty()) return
        ActualInterviewAPI.RegisterActualInterviewGrade(
            actualInterviewViewModel.saveActualInterviewGradeInfoList
                .distinctBy { it.movie_file_name }, // TODO sometimes there's duplicate w the same movie name
            actualInterviewViewModel.transcriptionList,
            requireActivity().getSharedPreferences(AppConstants.APP_PREF_NAME, MODE_PRIVATE),
            requireActivity().supportFragmentManager,
            { result: Int, error: Exception? ->
                if (error != null) {
                    CommonDialogFragment.newInstance(
                        "模擬面接結果更新処理でエラーが発生しました。",
                        true,
                        "OK",
                        object : CommonDialogFragment.CallbackListener {
                            override fun callbackFromDialogCloseButton() {
                                failureCallback()
                            }

                            override fun callbackFromDialogCancelButton() {
                            }
                        }
                    ).show(
                        parentFragmentManager,
                        "CommonDialogFragment"
                    )
                } else {
                    if (result >= 0) {
                        actualInterviewViewModel.saveActualInterviewGradeInfoList.forEach {
                            it.actual_interview_grade_id = result
                        }
                        successCallback()
                    } else {
                        CommonDialogFragment.newInstance(
                            "模擬面接結果メモ更新処理でエラーが発生しました。",
                            true,
                            "OK",
                            object : CommonDialogFragment.CallbackListener {
                                override fun callbackFromDialogCloseButton() {
                                    failureCallback()
                                }

                                override fun callbackFromDialogCancelButton() {
                                }
                            }
                        ).show(
                            parentFragmentManager,
                            "CommonDialogFragment"
                        )
                    }
                }
            })
    }

    private fun showDetail(isPaidUser: Boolean) {
        val sharedPreferences = requireActivity().getSharedPreferences(AppConstants.APP_PREF_NAME, MODE_PRIVATE)

        // CustomerIOへデータ送信
        CustomerIOActualinterviewUtil.clickedViewScoreResult()

        // 課金modal
        LoginLogoutUtil.showPaidModal(
            "interview",
            isPaidUser,
            R.id.action_navigation_interview_result_to_billing_introduction,
            true,
            "採点結果の詳細情報を閲覧するにはログインまたは会員登録後Premiumプランに登録してください。",
            true,
            "採点結果の詳細情報を閲覧するにはPremiumプラン登録してください。",
            sharedPreferences,
            parentFragmentManager,
            this,
            requireContext(),
            requireActivity().application as OPELabApplication
        )
    }

    private fun handleBackPress(popUpToNavId: Int = R.id.action_navigation_actual_interview_result_to_navigation_actual_interview_top) {
        // 解析結果を保存し、その後メモを保存する
        //if (actualInterviewViewModel.saveActualInterviewGradeInfoList.isNotEmpty()) {
        if (binding.saveAnalyze.isChecked) {
            registerActualInterviewGradeAndMemo(
                {
                    // Upload video files if they exist
                    uploadActualInterviewVideos {
                        // 遷移
                        Handler(Looper.getMainLooper()).post {
                            findNavController().navigate(
                                popUpToNavId,
                                null,
                                NavOptions.Builder()
                                    .setPopUpTo(
                                        R.id.navigation_actual_interview_top,
                                        true
                                    )
                                    .build()
                            )
                            val navView: BottomNavigationView =
                                activity?.findViewById(R.id.nav_view)!!
                            navView.visibility = View.VISIBLE
                        }
                    }
                },
                {
                    // 何もしない
                }
            )
        } else {
            // 保存しないので動画ファイルは削除
            actualInterviewViewModel.actualInterviewGradeInfoList.forEach { actualInterviewGradeInfo ->
                if (actualInterviewGradeInfo.movie_file_name.isNotEmpty()) {
                    val movieFile = File(
                        requireContext().getDir(AppConstants.MOVIE_DIR_NAME, MODE_PRIVATE),
                        actualInterviewGradeInfo.movie_file_name
                    )
                    if (movieFile.exists() && movieFile.isFile) {
                        movieFile.delete()
                    }
                }
            }

            // 保存せずそのまま遷移
            findNavController().navigate(
                popUpToNavId,
                null,
                NavOptions.Builder()
                    .setPopUpTo(
                        R.id.navigation_actual_interview_top,
                        true
                    )
                    .build()
            )
            val navView: BottomNavigationView = activity?.findViewById(R.id.nav_view)!!
            navView.visibility = View.VISIBLE
        }
    }

    // -1 = left, 1 = right
    fun navigatePager(direction:Int) {
        val currentPos = binding.viewPager.currentItem
        val totalItem = binding.viewPager.adapter?.itemCount ?: 0
        if(direction == -1) {
            if((currentPos-1) >= 0)  binding.viewPager.currentItem = currentPos - 1
        } else {
            if((currentPos+1) < totalItem) binding.viewPager.currentItem = currentPos + 1
        }
    }

    private fun uploadActualInterviewVideos(onComplete: () -> Unit) {
        val sharedPreferences = requireActivity().getSharedPreferences(AppConstants.APP_PREF_NAME, MODE_PRIVATE)
        val videoUploadCount = actualInterviewViewModel.saveActualInterviewGradeInfoList.count {
            it.movie_file_name.isNotEmpty() && it.actual_interview_grade_id > 0
        }
        // If no videos to upload, complete immediately
        if (videoUploadCount == 0) {
            onComplete()
            return
        }
        var uploadedCount = 0
        var hasError = false
        actualInterviewViewModel.saveActualInterviewGradeInfoList.forEach { gradeInfo ->
            val movieFile = File(
                requireContext().getDir(AppConstants.MOVIE_DIR_NAME, MODE_PRIVATE),
                gradeInfo.movie_file_name
            )

            // Skip if file doesn't exist
            if (!movieFile.exists() || !movieFile.isFile) {
                uploadedCount++
                if (uploadedCount >= videoUploadCount && !hasError) {
                    onComplete()
                }
                return@forEach
            }

            // Get S3 upload URL
            ActualInterviewAPI.GetActualInterviewVideoUploadUrl(
                gradeInfo.movie_file_name,
                "video/mp4",
                sharedPreferences,
                requireActivity().supportFragmentManager,
                { uploadUrl, error ->
                    if (error != null || uploadUrl == null) {
                        hasError = true
                        uploadedCount++
                        if (uploadedCount >= videoUploadCount) {
                            // Continue even if there was an error
                            onComplete()
                        }
                        return@GetActualInterviewVideoUploadUrl
                    }

                    // Upload video to S3
                    ActualInterviewAPI.UploadVideoToS3(
                        uploadUrl,
                        movieFile,
                        "video/mp4",
                        { success, uploadError ->
                            uploadedCount++
                            if (!success) {
                                hasError = true
                            }
                            if (uploadedCount >= videoUploadCount) {
                                // Continue even if there was an error
                                onComplete()
                            }
                        }
                    )
                }
            )
        }
    }
}