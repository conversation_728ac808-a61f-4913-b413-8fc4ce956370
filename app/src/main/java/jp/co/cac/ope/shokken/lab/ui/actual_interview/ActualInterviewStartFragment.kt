package jp.co.cac.ope.shokken.lab.ui.actual_interview

import android.Manifest
import android.content.Context.MODE_PRIVATE
import android.content.SharedPreferences
import android.content.pm.PackageManager
import android.graphics.Bitmap
import android.graphics.Rect
import android.media.Image
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.TextureView
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.ImageButton
import android.widget.TextView
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.OptIn
import androidx.core.content.ContextCompat
import androidx.core.net.toUri
import androidx.core.view.isVisible
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.common.util.UnstableApi
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.ui.PlayerView
import androidx.navigation.fragment.findNavController
import com.affectiva.vision.Face
import com.affectiva.vision.Frame
import com.affectiva.vision.Frame.Rotation
import com.arthenica.ffmpegkit.FFmpegKit
import com.google.android.material.bottomnavigation.BottomNavigationView
import jp.co.cac.ope.shokken.lab.Affectiva.AV.CameraLibrary
import jp.co.cac.ope.shokken.lab.Affectiva.AV.CameraLibrary.CameraLibraryEventListener
import jp.co.cac.ope.shokken.lab.Affectiva.AsyncFrameDetector
import jp.co.cac.ope.shokken.lab.Affectiva.OnDetectorEventListener
import jp.co.cac.ope.shokken.lab.Affectiva.ResultManager.EmotionValuesManager
import jp.co.cac.ope.shokken.lab.AppConstants
import jp.co.cac.ope.shokken.lab.R
import jp.co.cac.ope.shokken.lab.connects.ActualInterviewAPI
import jp.co.cac.ope.shokken.lab.databinding.FragmentActualInterviewStartBinding
import jp.co.cac.ope.shokken.lab.databinding.LayoutPlayerViewBinding
import jp.co.cac.ope.shokken.lab.model.ActualInterviewContentModel
import jp.co.cac.ope.shokken.lab.model.ActualInterviewGradeInfoModel
import jp.co.cac.ope.shokken.lab.model.ActualInterviewTranscriptionModel
import jp.co.cac.ope.shokken.lab.ui.modal.ActivityIndicatorDialogFragment
import jp.co.cac.ope.shokken.lab.ui.modal.CommonDialogFragment
import jp.co.cac.ope.shokken.lab.ui.modal.CountdownDialogFragment
import jp.co.cac.ope.shokken.lab.utils.CustomerIOActualinterviewUtil
import jp.co.cac.ope.shokken.lab.utils.DateUtil
import jp.co.cac.ope.shokken.lab.utils.ImageUtil
import jp.co.cac.ope.shokken.lab.utils.SharedPreferenceUtil
import org.json.JSONObject
import org.vosk.Recognizer
import org.vosk.android.RecognitionListener
import org.vosk.android.SpeechStreamService
import org.vosk.android.StorageService
import java.io.File
import java.io.FileInputStream
import java.text.SimpleDateFormat
import java.util.Date
import java.util.Locale
import java.util.TimeZone
import java.util.UUID

class ActualInterviewStartFragment : Fragment(), OnDetectorEventListener,
    CameraLibraryEventListener {

    private var _binding: FragmentActualInterviewStartBinding? = null

    // This property is only valid between onCreateView and
    // onDestroyView.
    private val binding get() = _binding!!

    private lateinit var actualInterviewStartViewModel: ActualInterviewStartViewModel
    private lateinit var actualInterviewViewModel: ActualInterviewViewModel

    private val indicator: ActivityIndicatorDialogFragment =
        ActivityIndicatorDialogFragment.newInstance()
    private val redoDialog: CommonDialogFragment =
        CommonDialogFragment.newInstance("自動でスタート画面に戻ります。\n撮影をやり直す際は、再度スタートボタンを押してください。")

    private var mCameraLibrary: CameraLibrary? = null
    private var mSensorRotate: Rotation = Rotation.CW_90
    private var mAsyncDetector: AsyncFrameDetector? = null
    private var mInitialMillis: Long = 0
    private var mTimestamp: Float = -1.0f
    private var mIsHispeedRespnce: Boolean = false
    private var mLastProcssTime: Float = -1.0f
    private var mLastRecieveTime: Float = -1.0f

    private var firstPlayer: ExoPlayer? = null
    private lateinit var lastPlayer: ExoPlayer
    private lateinit var readyingNormalPlayerView: PlayerView
    private lateinit var startingNormalPlayerView: PlayerView
    private var isFirstPlayerPlaying: Boolean = false
    private var isLastPlayerPlaying: Boolean = false

    private lateinit var interviewer_first: String
    private lateinit var interviewer_last: String

    // 音声認識結果
    private var speechIndex: Int = -1
    private var bestTranscription: ArrayList<String> = arrayListOf<String>()
    private var isFinal: ArrayList<Boolean> = arrayListOf<Boolean>()
    private var speechDuration: Long = -1L
    private var speechFinalCount: Int = 0

    // 動画保存
    private lateinit var tempOutputFile: File
    private lateinit var outputFile: File

    // 音声保存
    private var pcmFile: File? = null

    private var readyFlg: Boolean = true
    private var openRestartFlg: Boolean = false
    private var startingFlag: Boolean = false
    private var autoScrollFlg: Boolean = true
    private var isFileSaveFlg: Boolean = false
    private var firstVideoPlayerLoad: Boolean = false
    private var lastVideoPlayerLoad: Boolean = false

    private var isPreStart: Boolean = false
    private var isStart: Boolean = false

    private val permissionTimer = Handler(Looper.getMainLooper())
    private val permissionRunnable: Runnable = object : Runnable {
        override fun run() {
            permissionTimerUpdate()
            permissionTimer.postDelayed(this, 100L)
        }
    }

    private var interviewTime: Int = 0
    private var countUp: Int = 0
    private val interviewTimer = Handler(Looper.getMainLooper())
    private val interviewRunnable: Runnable = object : Runnable {
        override fun run() {
            if (!isResumed) return
            interviewTimerUpdate()
            interviewTimer.postDelayed(this, 1000L)
        }
    }
    private var prestartCount: Int = 3
    private var readyCount: Int = 3
    private var faceDetectTimeStart: Long = 0L
    private var faceDetectTimeEnd: Long = 0L
    private val afterProcessTimer = Handler(Looper.getMainLooper())
    private val afterProcessRunnable: Runnable = object : Runnable {
        override fun run() {
            if (!isResumed) return
            afterProcessTimerUpdate()
            afterProcessTimer.postDelayed(this, 1000L)
        }
    }
    private var afterProcessCount: Int = 2

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentActualInterviewStartBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        actualInterviewStartViewModel =
            ViewModelProvider(this)[ActualInterviewStartViewModel::class.java]
        actualInterviewViewModel =
            ViewModelProvider(requireActivity())[ActualInterviewViewModel::class.java]

        val navView: BottomNavigationView = activity?.findViewById(R.id.nav_view)!!
        navView.visibility = View.GONE

        initExoPlayer()

        // Fetch the interview list
        fetchMockInterviewList()

        if (ContextCompat.checkSelfPermission(
                requireContext(),
                Manifest.permission.CAMERA
            ) == PackageManager.PERMISSION_GRANTED && ContextCompat.checkSelfPermission(
                requireContext(),
                Manifest.permission.RECORD_AUDIO
            ) == PackageManager.PERMISSION_GRANTED
        ) {
            startCamera(binding.textureView)
        } else {
            requestCameraPermissions(binding.textureView)
            requestAudioPermissions()

            permissionTimer.postDelayed(permissionRunnable, 100L)
        }
    }

    fun permissionTimerUpdate() {
        if (ContextCompat.checkSelfPermission(
                requireContext(),
                Manifest.permission.CAMERA
            ) == PackageManager.PERMISSION_GRANTED && ContextCompat.checkSelfPermission(
                requireContext(),
                Manifest.permission.RECORD_AUDIO
            ) == PackageManager.PERMISSION_GRANTED
        ) {
            Handler(Looper.getMainLooper()).post {
                permissionTimer.removeCallbacks(permissionRunnable)
            }

            Handler(Looper.getMainLooper()).postDelayed({
                startCamera(binding.textureView)
            }, 500)
        }
    }

    override fun onPause() {
        Log.i("ActionLog", "onPause IN")

        if (mCameraLibrary != null) {
            mCameraLibrary!!.closeCamera();
            mCameraLibrary!!.stopBackgroundThread()
            mCameraLibrary!!.SetCameraLibraryEventListener(null)
            //mCameraLibrary = null
        }

        if (mAsyncDetector != null) {
            mAsyncDetector!!.setOnDetectorEventListener(null);
            if (mAsyncDetector!!.isRunning()) {
                //asyncDetector.stop()
                //asyncDetector.reset()
            }
        }

        if (firstPlayer != null && firstPlayer?.isPlaying!!) {
            firstPlayer?.pause()
        }

        if (::lastPlayer.isInitialized && lastPlayer.isPlaying) {
            lastPlayer.pause()
        }

        Log.i("ActionLog", "onPause OUT")

        super.onPause()
    }

    override fun onResume() {
        super.onResume()

        Log.d("ActionLog", "onResume IN")

        if (mCameraLibrary != null) {
            mCameraLibrary!!.SetCameraLibraryEventListener(this)
            mCameraLibrary!!.startBackgroundThread()
            mCameraLibrary!!.TryOpenCamera()
        }

        if (mAsyncDetector != null) {
            mAsyncDetector!!.setOnDetectorEventListener(this);
        }

        if (firstPlayer != null && isFirstPlayerPlaying) {
            firstPlayer?.play()
        }

        if (::lastPlayer.isInitialized && isLastPlayerPlaying) {
            lastPlayer.play()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()

        Log.i("ActionLog", "onDestroyView IN")

        if (mCameraLibrary != null) {
            mCameraLibrary!!.closeCamera();
            mCameraLibrary!!.stopBackgroundThread()
            mCameraLibrary!!.SetCameraLibraryEventListener(null)
            mCameraLibrary = null
        }

        if (mAsyncDetector != null) {
            mAsyncDetector!!.setOnDetectorEventListener(null);
            if (mAsyncDetector!!.isRunning()) {
                mAsyncDetector!!.stop()
                mAsyncDetector!!.reset()
                mAsyncDetector = null
            }
        }

        if (firstPlayer != null) {
            firstPlayer?.release()
        }

        if (::lastPlayer.isInitialized) lastPlayer.release()

        Handler(Looper.getMainLooper()).post {
            interviewTimer.removeCallbacks(interviewRunnable)
            afterProcessTimer.removeCallbacks(afterProcessRunnable)
        }

        _binding = null

        Log.i("ActionLog", "onDestroyView OUT")
    }

    /**
     * Fetch the mock interview list from the API
     */
    private fun fetchMockInterviewList() {
        val sharedPreferences =
            requireActivity().getSharedPreferences(AppConstants.APP_PREF_NAME, MODE_PRIVATE)
        indicator.show(parentFragmentManager, "ActivityIndicatorDialogFragment")
        ActualInterviewAPI.GetListActualInterviewContent(
            sharedPreferences,
            parentFragmentManager,
            true,
            { result: ArrayList<ActualInterviewContentModel>, error: Exception? ->
                requireActivity().runOnUiThread {
                    if (error == null && result.isNotEmpty()) {
                        result.forEach { item ->
                            // ユーザ設定のモードと同じかすべて表示のコンテンツのみ表示
                            if (item.disp_mode == 0 || item.disp_mode == SharedPreferenceUtil.getMode(
                                    sharedPreferences
                                )
                            ) {
                                actualInterviewViewModel.interviewList.add(item)
                            }
                        }

                        // ユーザ設定のモードによって並び順を変える
                        if (SharedPreferenceUtil.getMode(sharedPreferences) == 1) {
                            actualInterviewViewModel.interviewList.sortBy { it.order_new }
                        } else {
                            actualInterviewViewModel.interviewList.sortBy { it.order_change }
                        }

                        //actualInterviewViewModel.interviewList = ArrayList(result.take(2))

                        if (actualInterviewViewModel.interviewList.isNotEmpty()) {
                            actualInterviewViewModel.currentInterviewIndex = 0
                            actualInterviewViewModel.selectInterview =
                                actualInterviewViewModel.interviewList[0]

                            resetInterviewState()

                            onInterviewContentChange()
                        }
                        indicator.dismissWithAnimation()
                    } else {
                        indicator.dismissWithAnimation()
                        CommonDialogFragment.newInstance(
                            "通信に失敗しました。",
                            true,
                            "OK",
                            object : CommonDialogFragment.CallbackListener {
                                override fun callbackFromDialogCloseButton() {
                                    requireActivity().onBackPressedDispatcher.onBackPressed()
                                }

                                override fun callbackFromDialogCancelButton() {}
                            }
                        ).show(parentFragmentManager, "CommonDialogFragment")
                    }
                }
            }
        )
    }

    private fun onInterviewContentChange() {
        val sharedPreferences =
            requireActivity().getSharedPreferences(AppConstants.APP_PREF_NAME, MODE_PRIVATE)

        // 初期値はとりあえず男性
        interviewer_first =
            actualInterviewViewModel.selectInterview?.interviewer_url_man_first.toString()
        interviewer_last =
            actualInterviewViewModel.selectInterview?.interviewer_url_man_last.toString()

        // 設定が女性もしくは設定がランダム
        if (SharedPreferenceUtil.getMockInterviewer(sharedPreferences) == 2
            || (SharedPreferenceUtil.getMockInterviewer(sharedPreferences) == 0 && (1..2).random() == 2)
        ) {
            interviewer_first =
                actualInterviewViewModel.selectInterview?.interviewer_url_woman_first.toString()
            interviewer_last =
                actualInterviewViewModel.selectInterview?.interviewer_url_woman_last.toString()
        }

        firstPlayer?.apply {
            playWhenReady = false

            // 再生する動画の URL を指定
            val mediaItem = MediaItem.fromUri(interviewer_first.toUri())
            setMediaItem(mediaItem)

            prepare()
        }

        lastPlayer.apply {
            playWhenReady = false

            // 再生する動画の URL を指定
            val mediaItem = MediaItem.fromUri(interviewer_last.toUri())
            setMediaItem(mediaItem)

            prepare()
        }

        initView()

        setupReadying()
        setupStarting()

        startingNormalPlayerView.visibility = View.GONE
        readyingNormalPlayerView.visibility = View.VISIBLE

        // 最初の位置はReadyingNormal基準
        binding.readyingNormal.topStatus.viewTreeObserver.addOnGlobalLayoutListener(object :
            ViewTreeObserver.OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                binding.readyingNormal.topStatus.viewTreeObserver.removeOnGlobalLayoutListener(this)

                binding.previewLayout.y = binding.readyingNormal.topStatus.y
            }
        })
    }

    private fun initExoPlayer() {
        firstPlayer?.release()
        if (::lastPlayer.isInitialized) lastPlayer.release()
        firstPlayer = ExoPlayer.Builder(requireActivity()).build()
        lastPlayer = ExoPlayer.Builder(requireActivity()).build()

        firstPlayer?.addListener(object : Player.Listener {
            override fun onPlaybackStateChanged(state: Int) {
                when (state) {
                    Player.STATE_READY -> {
                        firstVideoPlayerLoad = true

                        if (lastVideoPlayerLoad) {
                            // 通常
                            binding.readyingNormal.startButton.isEnabled = true
                            binding.readyingNormal.startButton.setBackgroundResource(R.drawable.round_rectangle_25dp_enji)
                        }
                    }

                    Player.STATE_ENDED -> {
                        isFirstPlayerPlaying = false

                        // カウントダウン開始
                        startMockInterview()
                    }
                }
            }
        })

        lastPlayer.addListener(object : Player.Listener {
            override fun onPlaybackStateChanged(state: Int) {
                when (state) {
                    Player.STATE_READY -> {
                        lastVideoPlayerLoad = true

                        if (firstVideoPlayerLoad) {
                            // 通常
                            binding.readyingNormal.startButton.isEnabled = true
                            binding.readyingNormal.startButton.setBackgroundResource(R.drawable.round_rectangle_25dp_enji)
                        }
                    }

                    Player.STATE_ENDED -> {
                        isLastPlayerPlaying = false
                    }
                }
            }
        })
    }

    /**
     * Reset the interview state variables for a new interview
     */
    private fun resetInterviewState() {
        // Reset all state variables
        readyFlg = true
        startingFlag = false
        isPreStart = false
        isStart = false

        // Reset recording variables
        speechIndex = -1
        bestTranscription = arrayListOf()
        isFinal = arrayListOf()
        speechDuration = -1L

        // Reset UI state
        binding.readyingNormal.constraintLayout.visibility = View.VISIBLE
        binding.startingNormal.constraintLayout.visibility = View.GONE
        if (::readyingNormalPlayerView.isInitialized) {
            readyingNormalPlayerView.visibility = View.VISIBLE
        }
        if (::startingNormalPlayerView.isInitialized) {
            startingNormalPlayerView.visibility = View.GONE
        }
        binding.readyingNormal.preStartFooterLayout.visibility = View.GONE
        binding.readyingNormal.readyingFooterLayout.visibility = View.VISIBLE
    }

    /**
     * Save the current interview data before moving to the next one
     */
    private fun saveCurrentInterviewData() {
        val alreadyAdded = actualInterviewViewModel.completedInterviews.find {
            it.interview.actual_interview_contents_id == actualInterviewViewModel.selectInterview!!.actual_interview_contents_id
        } != null
        if (alreadyAdded) return

        // Create a CompletedInterview object with current data
        val completedInterview = CompletedInterview(
            interview = actualInterviewViewModel.selectInterview!!,
            outputFile = outputFile,
            faceDetectData = actualInterviewViewModel.faceDetectData,
            transcription = bestTranscription.joinToString(""),
            speechDuration = speechDuration
        )
        actualInterviewViewModel.completedInterviews.add(completedInterview)
        actualInterviewViewModel.faceDetectData = arrayListOf()
    }

    private fun moveToNextInterview() {
        actualInterviewViewModel.currentInterviewIndex++
        if (actualInterviewViewModel.currentInterviewIndex >= actualInterviewViewModel.interviewList.size) {
            actualInterviewViewModel.currentInterviewIndex = 0
        }
        actualInterviewViewModel.selectInterview =
            actualInterviewViewModel.interviewList[actualInterviewViewModel.currentInterviewIndex]

        initExoPlayer()
        startingNormalPlayerView.player = lastPlayer
        readyingNormalPlayerView.player = firstPlayer
        onInterviewContentChange()
        reinitializeCamera()
        resetInterviewState()
    }

    /**
     * Reset to the first interview
     */
    private fun resetToFirstInterview() {
        actualInterviewViewModel.currentInterviewIndex = 0
        actualInterviewViewModel.completedInterviews.clear()
        actualInterviewViewModel.faceDetectData.clear()
        actualInterviewViewModel.transcriptionList.clear()
        actualInterviewViewModel.selectInterview =
            actualInterviewViewModel.interviewList.firstOrNull()

        initExoPlayer()
        startingNormalPlayerView.player = lastPlayer
        readyingNormalPlayerView.player = firstPlayer
        onInterviewContentChange()
        reinitializeCamera()
        resetInterviewState()
    }

    /**
     * Reinitialize the camera to ensure MediaRecorder is properly set up for the next interview
     */
    private fun reinitializeCamera() {
        if (mCameraLibrary != null) {
            mCameraLibrary!!.closeCamera()
            mCameraLibrary!!.stopBackgroundThread()
            mCameraLibrary!!.SetCameraLibraryEventListener(null)
        }

        startCamera(binding.textureView)
    }

    /**
     * Process all completed interviews and show results
     */
    private fun processAllInterviews() {
        indicator.show(parentFragmentManager, "ActivityIndicatorDialogFragment")
        actualInterviewViewModel.actualInterviewGradeInfoList.clear()
        actualInterviewViewModel.saveActualInterviewGradeInfoList.clear()
        val sharedPreferences =
            requireActivity().getSharedPreferences(AppConstants.APP_PREF_NAME, MODE_PRIVATE)

        processTranscription(sharedPreferences) {
            processExpression(sharedPreferences) { error ->
                indicator.dismissWithAnimation()

                if (error == null) {
                    // CustomerIOへデータ送信 (using the first result for analytics)
                    CustomerIOActualinterviewUtil.completedActualinterview(
                        (faceDetectTimeEnd - faceDetectTimeStart).toDouble(),
                        actualInterviewViewModel.actualInterviewGradeInfoList.first()
                    )
                    Handler(Looper.getMainLooper()).post {
                        findNavController().popBackStack()
                        findNavController().navigate(R.id.action_navigation_actual_interview_face_adjust_to_navigation_actual_interview_result)
                    }
                } else {
                    CommonDialogFragment.newInstance(
                        "模擬面接の分析に失敗しました。",
                        true,
                        "OK",
                        object : CommonDialogFragment.CallbackListener {
                            override fun callbackFromDialogCloseButton() {
                                // Reset camera on error
                                mCameraLibrary!!.SetCameraLibraryEventListener(this@ActualInterviewStartFragment)
                                mCameraLibrary!!.startBackgroundThread()
                                mCameraLibrary!!.TryOpenCamera()
                            }

                            override fun callbackFromDialogCancelButton() {
                            }
                        }
                    ).show(parentFragmentManager, "CommonDialogFragment")
                }
            }
        }
    }

    private fun processExpression(
        sharedPreferences: SharedPreferences,
        completion: (Exception?) -> Unit
    ) {
        actualInterviewViewModel.completedInterviews.forEachIndexed { index, completedInterview ->
            val practiceIndexId = completedInterview.interview.practice_index_id
            val faceData = completedInterview.faceDetectData

            ActualInterviewAPI.GetExpressionAnalysisResult(
                practiceIndexId,
                faceData,
                sharedPreferences,
                parentFragmentManager,
                { result: ActualInterviewGradeInfoModel, error: Exception? ->
                    if (error != null) {
                        error.printStackTrace()
                    } else {
                        result.actual_interview_contents_id =
                            completedInterview.interview.actual_interview_contents_id
                        if (completedInterview.outputFile != null) {
                            result.movie_file_name = completedInterview.outputFile.name
                        }
                        actualInterviewViewModel.actualInterviewGradeInfoList.add(result)
                        actualInterviewViewModel.saveActualInterviewGradeInfoList.add(result)
                    }

                    if (index != (actualInterviewViewModel.completedInterviews.size - 1)) return@GetExpressionAnalysisResult

                    if (actualInterviewViewModel.actualInterviewGradeInfoList.isNotEmpty()) {
                        completion(null)
                    } else {
                        completion(Exception("No results"))
                    }
                }
            )
        }
    }

    private fun processTranscription(sharedPreferences: SharedPreferences, completion: () -> Unit) {
        val formatter: SimpleDateFormat = DateUtil.getJPFMT() as SimpleDateFormat
        formatter.timeZone = TimeZone.getTimeZone("UTC")
        formatter.applyPattern("HH:mm:ss")
        var interview_script = "" // Leave blank, because no MySelf mode

        actualInterviewViewModel.transcriptionList =
            MutableList(actualInterviewViewModel.completedInterviews.size) { null }
        actualInterviewViewModel.completedInterviews.forEachIndexed { index, completedInterview ->
            ActualInterviewAPI.GetTranscriptionAnalysisResult(
                completedInterview.transcription.toString(),
                interview_script,
                formatter.format(Date(completedInterview.speechDuration)),
                sharedPreferences,
                parentFragmentManager,
                { result: ActualInterviewTranscriptionModel, error: Exception? ->
                    actualInterviewViewModel.transcriptionList[index] = result
                    if (index != (actualInterviewViewModel.completedInterviews.size - 1)) return@GetTranscriptionAnalysisResult
                    completion()
                }
            )
        }
    }

    override fun onDetach() {
        Log.i("ActionLog", "onDetach IN");

        Log.i("ActionLog", "onDetach OUT");

        super.onDetach();
    }

    // 画面初期表示
    private fun initView() {
        interviewTime =
            DateUtil.string2Minutes(actualInterviewViewModel.selectInterview?.estimated_time.toString()) * 60

        // 開始、終了のカウント用
        prestartCount = 3
        readyCount = 3
        readyFlg = true
        isFileSaveFlg = false

        openRestartFlg = false
        startingFlag = false

        isPreStart = false
        isStart = false

        autoScrollFlg = true

        binding.startingNormal.recording.visibility = View.GONE

        binding.startingNormal.endButton.visibility = View.VISIBLE
    }

    @OptIn(UnstableApi::class)
    private fun setupReadying() {
        val titleTextview: TextView = binding.readyingNormal.backHeaderBar.headerTitle
        actualInterviewStartViewModel.titleText.observe(viewLifecycleOwner) {
            titleTextview.text = it
        }

        val backButton: ImageButton = binding.readyingNormal.backHeaderBar.backButton
        backButton.setOnClickListener {
            requireActivity().onBackPressedDispatcher.onBackPressed()
        }

        if (actualInterviewViewModel.skipMyself) {
            binding.readyingNormal.textView.text = "2/2"
        } else {
            binding.readyingNormal.textView.text = "3/3"
        }

        binding.readyingNormal.timeCountLabel.text = "Ready"
        binding.readyingNormal.kindLabel.text =
            actualInterviewViewModel.selectInterview?.practice_index_name

        binding.readyingNormal.targetTime.text = String.format(
            Locale.JAPAN,
            "%d分",
            DateUtil.string2Minutes(actualInterviewViewModel.selectInterview?.estimated_time.toString())
        )

        if (!::readyingNormalPlayerView.isInitialized) {
            val readyingNormalPlayerViewBinding =
                LayoutPlayerViewBinding.inflate(layoutInflater, null, false)
            readyingNormalPlayerViewBinding.root.id = View.generateViewId()
            readyingNormalPlayerView = readyingNormalPlayerViewBinding.playerView
            readyingNormalPlayerView.player = firstPlayer
            binding.readyingNormal.movieLayout.addView(readyingNormalPlayerViewBinding.root, 0)
        }

        binding.readyingNormal.startButton.setOnClickListener({
            CountdownDialogFragment.newInstance(object : CountdownDialogFragment.CountdownListener {
                override fun onCountdownFinished() {
                    //actualInterviewStartViewModel.onStartNormalFirstClick()
                    binding.readyingNormal.readyingFooterLayout.visibility = View.GONE
                    binding.readyingNormal.preStartFooterLayout.visibility = View.VISIBLE
                    isPreStart = true
                    isStart = false
                    isFirstPlayerPlaying = true
                    firstPlayer?.play()
                }
            }).show(parentFragmentManager, "CountdownDialogFragment")
        })

        binding.readyingNormal.redoImage.setOnClickListener({
            redoActualinterview()
        })

        binding.readyingNormal.redoText.setOnClickListener({
            redoActualinterview()
        })
    }

    private fun setupStarting() {
        val titleTextview: TextView = binding.startingNormal.headerBar.headerTitle
        actualInterviewStartViewModel.titleText.observe(viewLifecycleOwner) {
            titleTextview.text = it
            titleTextview.gravity = Gravity.CENTER
        }

        binding.startingNormal.headerBar.backButton.visibility = View.GONE

        binding.startingNormal.seekBar.isVertical = false
        binding.startingNormal.seekBar.seekPoint = 0.0f
        binding.startingNormal.seekBar.invalidate()

        binding.startingNormal.recording.visibility = View.GONE

        binding.startingNormal.timeCountLabel.text = "00:00"

        binding.startingNormal.kindLabel.text =
            actualInterviewViewModel.selectInterview?.practice_index_name

        binding.startingNormal.targetTime.text = String.format(
            Locale.JAPAN,
            "%d分",
            DateUtil.string2Minutes(actualInterviewViewModel.selectInterview?.estimated_time.toString())
        )

        if (!::startingNormalPlayerView.isInitialized) {
            val startingNormalPlayerViewBinding =
                LayoutPlayerViewBinding.inflate(layoutInflater, null, false)
            startingNormalPlayerViewBinding.root.id = View.generateViewId()
            startingNormalPlayerView = startingNormalPlayerViewBinding.playerView
            startingNormalPlayerView.player = lastPlayer
            binding.startingNormal.movieLayout.addView(startingNormalPlayerViewBinding.root, 0)
        }

        binding.startingNormal.endButton.setOnClickListener({
            indicator.show(parentFragmentManager, "ActivityIndicatorDialogFragment")
            stopMockInterview {
                indicator.dismissWithAnimation()
                showInterviewEndDialog(true)
            }
        })
    }

    private fun showInterviewEndDialog(showRetryButton: Boolean) {
        val isLastInterview =
            actualInterviewViewModel.currentInterviewIndex == (actualInterviewViewModel.interviewList.size - 1)
        CommonDialogFragment.newInstance(
            if (isLastInterview) {
                if (showRetryButton) {
                    "終了しますか？"
                } else {
                    "制限時間を経過したため自動的に終了します。"
                }
            } else {
                "Do you want to go to the next question?"
            },
            true,
            if (isLastInterview) {
                if (showRetryButton) {
                    "結果を見る"
                } else {
                    "閉じる"
                }
            } else {
                "Next Question"
            },
            object : CommonDialogFragment.CallbackListener {
                override fun callbackFromDialogCloseButton() {
                    // CustomerIOへデータ送信
                    CustomerIOActualinterviewUtil.completedShootingActualinterview()
                    saveCurrentInterviewData()
                    if (isLastInterview) {
                        processAllInterviews()
                    } else {
                        moveToNextInterview()
                    }
                }

                override fun callbackFromDialogCancelButton() {
                    //redoActualinterview(true)
                    resetToFirstInterview()
                }
            },
            showRetryButton,
            if (showRetryButton) {
                if (isLastInterview) {
                    "やり直す"
                } else "Retry"
            } else ""
        ).show(parentFragmentManager, "CommonDialogFragment")
    }

    private fun commonStartMockInterview() {
        binding.startingNormal.seekBar.isVertical = false
        binding.startingNormal.seekBar.seekPoint = 0.0f
        binding.startingNormal.seekBar.invalidate()

        prestartCount = 3
        readyCount = 3

        // カウントアップ
        countUp = 0

        binding.startingNormal.timeCountLabel.text =
            String.format(Locale.JAPAN, "%02d:%02d", countUp / 60, countUp % 60)

        autoScrollFlg = true
    }

    private fun startMockInterview() {
        readyFlg = false

        isPreStart = false
        isStart = true

        commonStartMockInterview()

        CountdownDialogFragment.newInstance(object : CountdownDialogFragment.CountdownListener {
            override fun onCountdownFinished() {
                CustomerIOActualinterviewUtil.startedShootingActualinterview()

                startingFlag = true

                binding.readyingNormal.constraintLayout.visibility = View.GONE
                binding.startingNormal.constraintLayout.visibility = View.VISIBLE

                readyingNormalPlayerView.visibility = View.GONE
                startingNormalPlayerView.visibility = View.VISIBLE

                // 切り替わった位置はstartingNormal基準
                binding.startingNormal.topStatus.viewTreeObserver.addOnGlobalLayoutListener(
                    object :
                        ViewTreeObserver.OnGlobalLayoutListener {
                        override fun onGlobalLayout() {
                            binding.startingNormal.topStatus.viewTreeObserver.removeOnGlobalLayoutListener(
                                this
                            )
                            binding.previewLayout.y = binding.startingNormal.topStatus.y
                        }
                    })

                // 録画開始
                startRecording()

                // 面接時間計測開始
                interviewTimer.postDelayed(interviewRunnable, 1000L)

                // 面接官動画再生開始
                lastPlayer.play()

                // CustomerIOへデータ送信
                faceDetectTimeStart = System.currentTimeMillis()
            }
        }).show(parentFragmentManager, "CountdownDialogFragment")
    }

    private fun stopMockInterview(onDone: (success: Boolean) -> Unit) {
        Handler(Looper.getMainLooper()).post {
            interviewTimer.removeCallbacks(interviewRunnable)
        }
        // 録画停止
        mCameraLibrary!!.closeCamera();
        mCameraLibrary!!.stopBackgroundThread()
        mCameraLibrary!!.SetCameraLibraryEventListener(null)
        // ファイル保存後にフラグ下げ
        tempOutputFile.let { file ->
            Log.d("ActionLog", "OutputFile: " + tempOutputFile.absolutePath + " DONE")

            FFmpegKit.executeAsync("-i ${tempOutputFile.absolutePath} -vf hflip ${outputFile.absolutePath}") { session ->
                val returnCode = session.returnCode
                if (returnCode.isValueSuccess) {
                    // 変換したので削除
                    if (tempOutputFile.exists() && tempOutputFile.isFile) {
                        tempOutputFile.delete()
                    }

                    Log.d("SpeechLog", "Input: ${outputFile.absolutePath}")
                    Log.d("SpeechLog", "Output: ${pcmFile?.absolutePath}")

                    FFmpegKit.executeAsync("-i ${outputFile.absolutePath} -vn -acodec pcm_s16le -ar 16000 -ac 1 ${pcmFile?.absolutePath}") { session ->
                        val returnCode = session.returnCode
                        if (returnCode.isValueSuccess) {
                            Log.d("SpeechLog", "変換成功！")

                            StorageService.unpack(
                                requireContext(), "model-small-ja", "model",
                                { model ->
                                    val recognizer: Recognizer = Recognizer(model, 16000.0f)

                                    val ais: FileInputStream = pcmFile?.inputStream()!!
                                    //if (ais.skip(44L) != 44L)

                                    var speechStreamService: SpeechStreamService =
                                        SpeechStreamService(recognizer, ais, 16000.0f);
                                    speechStreamService.start(
                                        object : RecognitionListener {
                                            override fun onResult(hypothesis: String?) {
                                                hypothesis?.let {
                                                    val jsonObj = JSONObject(it)
                                                    val text = jsonObj.optString("text", "")

                                                    Log.d("SpeechLog", "onResult: ${text}")

                                                    bestTranscription[speechIndex] = text
                                                    isFinal[speechIndex] = true;

                                                    speechIndex += 1
                                                    bestTranscription.add("")
                                                    isFinal.add(false)
                                                }
                                            }

                                            override fun onPartialResult(hypothesis: String?) {
                                                Log.d(
                                                    "SpeechLog",
                                                    "onPartialResult: ${hypothesis}"
                                                )
                                            }

                                            override fun onFinalResult(hypothesis: String?) {
                                                hypothesis?.let {
                                                    val jsonObj = JSONObject(it)
                                                    val text = jsonObj.optString("text", "")

                                                    Log.d("SpeechLog", "onFinalResult: ${text}")

                                                    bestTranscription[speechIndex] = text
                                                    isFinal[speechIndex] = true;

                                                    isFileSaveFlg = false

                                                    Log.d(
                                                        "SpeechLog",
                                                        "onFinalResult DONE: ${bestTranscription}"
                                                    )
                                                }

                                                onDone(true)
                                            }

                                            override fun onError(exception: Exception?) {
                                                Log.d("SpeechLog", "onError: ${exception}")
                                                onDone(false)
                                            }

                                            override fun onTimeout() {
                                                Log.d("SpeechLog", "onTimeout")
                                                onDone(false)
                                            }
                                        })
                                },
                                { exception ->
                                    Log.e(
                                        "SpeechLog",
                                        "Failed to unpack the model: ${exception.message}"
                                    )
                                    onDone(false)
                                }
                            )
                        } else {
                            Log.e("SpeechLog", "変換失敗: ${session.failStackTrace}")

                            session.allLogs.forEach { log ->
                                Log.d("SpeechLog", log.message)
                            }

                            // 標準出力 (stdout)
                            val output = session.output
                            Log.d("SpeechLog", "Output: $output")

                            // 標準エラー (stderr)
                            val failStackTrace = session.failStackTrace
                            Log.e("SpeechLog", "Fail: $failStackTrace")

                            if (pcmFile != null && pcmFile?.exists()!! && pcmFile?.isFile!!) {
                                pcmFile?.delete()
                            }

                            speechIndex = 0
                            bestTranscription = arrayListOf<String>()
                            isFinal = arrayListOf<Boolean>()
                            bestTranscription.add("音声認識処理が失敗しました")
                            isFinal.add(true)

                            isFileSaveFlg = false

                            onDone(false)
                        }
                    }
                } else {
                    onDone(false)
                }
            }
        }
        // 停止時点の経過時間を保持
        if (speechDuration != -1L) {
            speechDuration = System.currentTimeMillis() - speechDuration
        }
        // 経過時間が記録されていない場合は0秒としておく
        else {
            speechDuration = 0L
        }

        readyFlg = true

        isPreStart = false
        isStart = false

        // スタートに備えて
        prestartCount = 3
        readyCount = 3

        // CustomerIO用に終了時のタイムスタンプを保存
        faceDetectTimeEnd = System.currentTimeMillis()

        lastPlayer.pause()
    }

    private fun redoActualinterview(backToFirstInterview: Boolean = false) {
        Log.d("ActionLog", "functionName: redoActualinterview, message: やり直す")
        startingFlag = false

        isPreStart = false
        isStart = true

        // Reset all layout visibilities to ensure proper state
        binding.startingNormal.constraintLayout.visibility = View.GONE
        binding.readyingNormal.constraintLayout.visibility = View.VISIBLE
        binding.readyingNormal.preStartFooterLayout.visibility = View.GONE
        binding.readyingNormal.readyingFooterLayout.visibility = View.VISIBLE

        // 最初の位置はReadyingNormal基準
        binding.readyingNormal.topStatus.viewTreeObserver.addOnGlobalLayoutListener(object :
            ViewTreeObserver.OnGlobalLayoutListener {
            override fun onGlobalLayout() {
                binding.readyingNormal.topStatus.viewTreeObserver.removeOnGlobalLayoutListener(this)

                binding.previewLayout.y = binding.readyingNormal.topStatus.y
            }
        })
        // 後処理
        firstPlayer?.pause()
        firstPlayer?.seekTo(0)
        lastPlayer.pause()
        lastPlayer.seekTo(0)

        // CustomerIOへデータ送信
        // 解析結果を持ってないのでresultは送らない
        CustomerIOActualinterviewUtil.completedActualinterview(
            (faceDetectTimeEnd - faceDetectTimeStart).toDouble(),
            null
        )

        if (backToFirstInterview) {
            resetToFirstInterview()
        } else {
            // 2秒後にリセット処理
            afterProcessCount = 2
            afterProcessTimer.postDelayed(afterProcessRunnable, 1000L)
        }
    }

    private fun startRecording() {
        binding.startingNormal.endButton.visibility = View.VISIBLE
        // 動画保存に関する注意非表示
        binding.startingNormal.seekBar.isVertical = false
        binding.startingNormal.seekBar.seekPoint = 0.0f
        binding.startingNormal.seekBar.invalidate()

        // Affdex用のタイムスタンプをリセット
        faceDetectTimeStart = System.currentTimeMillis()
        faceDetectTimeEnd = System.currentTimeMillis()
        // 保持している認識結果リストをリセット
        actualInterviewViewModel.faceDetectData = arrayListOf<MutableMap<String, Any>>()

        // 録画開始
        mCameraLibrary?.startRecordingVideo()

        // 音声認識用のオーディオ設定
        speechIndex = 0
        bestTranscription = arrayListOf<String>()
        isFinal = arrayListOf<Boolean>()
        bestTranscription.add("")
        isFinal.add(false)
        speechDuration = System.currentTimeMillis()

        isFileSaveFlg = true

        binding.startingNormal.recording.visibility = View.VISIBLE
    }

    private fun interviewTimerUpdate() {
        countUp += 1

        binding.startingNormal.timeCountLabel.text =
            String.format(Locale.JAPAN, "%02d:%02d", countUp / 60, countUp % 60)

        if (interviewTime > countUp) {
            binding.startingNormal.seekBar.seekPoint = countUp.toFloat() / interviewTime.toFloat()
        } else {
            binding.startingNormal.seekBar.seekPoint = 1.0f
        }
        binding.startingNormal.seekBar.invalidate()

        Log.d("ActionLog", "seekPoint: " + binding.startingNormal.seekBar.seekPoint.toString())

        // 10秒程度余力を持つ
        if (countUp - interviewTime >= AppConstants.MOCK_INTERVIEW_OVER_TIME) {
            indicator.show(parentFragmentManager, "ActivityIndicatorDialogFragment")
            stopMockInterview {
                indicator.dismissWithAnimation()
                showInterviewEndDialog(false)
            }
        }
    }

    private fun afterProcessTimerUpdate() {
        afterProcessCount -= 1

        // ファイルが保存され、指定時間経過した場合
        if (!isFileSaveFlg && afterProcessCount <= 0) {
            Handler(Looper.getMainLooper()).post {
                afterProcessTimer.removeCallbacks(afterProcessRunnable)
            }

            // 保存した動画データがある場合はやり直しの場合使わないので削除
            if (tempOutputFile.exists() && tempOutputFile.isFile) {
                tempOutputFile.delete()
            }

            if (outputFile.exists() && outputFile.isFile) {
                outputFile.delete()
            }

            if (pcmFile != null && pcmFile?.exists()!! && pcmFile?.isFile!!) {
                pcmFile?.delete()
            }

            mCameraLibrary!!.SetCameraLibraryEventListener(this)
            mCameraLibrary!!.startBackgroundThread()
            mCameraLibrary!!.TryOpenCamera()

            try {
                redoDialog.dismiss()
            } catch (e: Exception) {
                /**
                 * TODO error IllegalStateException: Fragment CommonDialogFragment not associated with a fragment manager.
                 * when listening to interviewee and press redo text
                 */
                e.printStackTrace()
            }

            // 画面のリセット処理
            initView()
        }
    }

    private fun requestCameraPermissions(textureView: TextureView) {
        Log.d("ActionLog", "requestCameraPermissions IN")

        val requestPermissionLauncherCamera =
            registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted ->
                if (!isGranted) {
                    Log.d("ActionLog", "カメラの権限が必要です")
                }
            }
        requestPermissionLauncherCamera.launch(Manifest.permission.CAMERA)

        Log.d("ActionLog", "requestCameraPermissions OUT")
    }

    private fun requestAudioPermissions() {
        Log.d("ActionLog", "requestAudioPermissions IN")

        val requestPermissionLauncherAudio =
            registerForActivityResult(ActivityResultContracts.RequestPermission()) { isGranted ->
                if (!isGranted) {
                    Log.d("ActionLog", "マイクの権限が必要です")
                }
            }
        requestPermissionLauncherAudio.launch(Manifest.permission.RECORD_AUDIO)

        Log.d("ActionLog", "requestAudioPermissions OUT")
    }

    private fun startCamera(textureView: TextureView) {
        Log.d("ActionLog", "startCamera IN")

        // ファイル名生成
        val formatter: SimpleDateFormat = DateUtil.getJPFMT() as SimpleDateFormat
        formatter.applyPattern("yyyyMMddHHmmss")
        val timestamp: String = formatter.format(Date())
        val uuid = UUID.randomUUID().toString().replace("-", "")
        val dir: File = requireContext().getDir(AppConstants.MOVIE_DIR_NAME, MODE_PRIVATE)
        val fileName =
            "actual_${timestamp}_${uuid}_q${actualInterviewViewModel.currentInterviewIndex + 1}"
        outputFile = File(dir, fileName + ".mp4")
        tempOutputFile = File(dir, fileName + "_flip.mp4")
        pcmFile = File(dir, fileName + ".wav")

        Log.d("ActionLog", "OutputFile: " + outputFile?.absolutePath)

        mCameraLibrary =
            CameraLibrary(requireActivity(), requireContext(), textureView, tempOutputFile)
        mCameraLibrary!!.SetCameraLibraryEventListener(this)
        mCameraLibrary!!.startBackgroundThread()
        mCameraLibrary!!.TryOpenCamera()

        AsyncFrameDetector.NewInstance(requireContext())
        mAsyncDetector = AsyncFrameDetector.getInstance()
        if (mAsyncDetector != null) {
            mInitialMillis = AsyncFrameDetector.getInitialMillis()
            mAsyncDetector!!.setOnDetectorEventListener(this)
        }

        Log.d("ActionLog", "startCamera OUT")
    }

    override fun OnOpenedCamera() {
        if (mCameraLibrary == null) return

        if (mCameraLibrary!!.mSensorOrientation == 90) {
            // mSensorRotate = Frame.ROTATE.BY_90_CW
            mSensorRotate = Rotation.CW_90
        } else if (mCameraLibrary!!.mSensorOrientation == 270) {
            // mSensorRotate = Frame.ROTATE.BY_90_CCW
            mSensorRotate = Rotation.CW_270
        }

        Log.d("ActionLog", "OnOpenedCamera " + mSensorRotate + "OUT")
    }

    override fun OnUpdatePreview(bytes: ByteArray?, width: Int, height: Int) {
        if (mAsyncDetector == null || !mAsyncDetector!!.isRunning() || mCameraLibrary == null) return

        if (binding.startingNormal.recording.isVisible) {
            Handler(Looper.getMainLooper()).post {
                OnUpdatePreviewSub(bytes, width, height)
            }
        }
    }

    override fun OnUpdate2ndPreview(image: Image, width: Int, height: Int) {
        val ratio: Int = (height.toFloat() / binding.textureView.width.toFloat()).toInt()
        val rotatedBitmap = ImageUtil.rotateAndMirrorBitmap(
            ImageUtil.imageToBitmap(image, width, height, ratio)!!,
            90.0f,
            true
        )
        updatePreview(rotatedBitmap)
    }

    fun updatePreview(bitmap: Bitmap) {
        if (bitmap != null && binding.textureView.isAvailable) {
            val canvas = binding.textureView.lockCanvas()
            canvas?.drawBitmap(
                bitmap,
                null,
                Rect(0, 0, binding.textureView.width, binding.textureView.height),
                null
            )
            binding.textureView.unlockCanvasAndPost(canvas!!)
        }
    }

    private fun OnUpdatePreviewSub(bytes: ByteArray?, width: Int, height: Int) {
        //Log.d("ActionLog", "OnUpdatePreviewSub IN")

        if (mAsyncDetector == null || !mAsyncDetector!!.isRunning() || mCameraLibrary == null) return

        var newtimestamp: Float = 0.0f
        var limit: Float = 0.0f

        if (mIsHispeedRespnce) {
            newtimestamp = (System.currentTimeMillis() - mInitialMillis).toFloat() / 100.0f;
            limit = 0.45f
        } else {
            newtimestamp = (System.currentTimeMillis() - mInitialMillis).toFloat() / 1000.0f;
            limit = 0.045f
        }

        val dur: Float = newtimestamp - mTimestamp;
        if (mTimestamp < 0 || dur > limit) {
            if (mAsyncDetector!!.process(bytes, width, height, mSensorRotate, newtimestamp)) {
                mTimestamp = newtimestamp
            }
        }

        //Log.d("ActionLog", "OnUpdatePreviewSub OUT")
    }

    override fun onImageResults(faces: List<Face?>?, image: Frame?, timeStamp: Float) {
        Log.d("ActionLog", "onImageResultsSub onImageResults")
        if (mCameraLibrary == null) return
        Log.d("ActionLog", "onImageResultsSub onImageResults camera not null")
        Handler(Looper.getMainLooper()).post {
            Log.d("ActionLog", "onImageResultsSub onImageResults handler called")
            onImageResultsSub(faces, image, timeStamp)
        }
    }

    private fun onImageResultsSub(faces: List<Face?>?, image: Frame?, timeStamp: Float) {
        //Log.d("ActionLog", "onImageResultsSub IN")

        if (mCameraLibrary == null) return

        val timeStamp: Float = timeStamp / 1000.0f
        mLastRecieveTime = (System.currentTimeMillis() - mInitialMillis).toFloat() / 1000.0f
        mLastProcssTime = mLastRecieveTime - timeStamp

        if (mCameraLibrary == null) return

        var detectList: MutableMap<String, Any> = mutableMapOf<String, Any>()

        val formatter: SimpleDateFormat = DateUtil.getJPFMT() as SimpleDateFormat
        formatter.timeZone = TimeZone.getTimeZone("UTC")
        formatter.applyPattern("HH:mm:ss.SSS")
        val timestamp = formatter.format(Date(System.currentTimeMillis() - faceDetectTimeStart))

        detectList["timestamp"] = timestamp
        if (faces.isNullOrEmpty()) {
            detectList["disgust"] = 0.0
            detectList["sadness"] = 0.0
            detectList["engagement"] = 0.0
            detectList["cheek_raise"] = 0.0
            detectList["smile"] = 0.0
            detectList["smirk"] = 0.0
            detectList["brow_furrow"] = 0.0
            detectList["eye_widen"] = 0.0
            detectList["inner_brow_raise"] = 0.0
            detectList["brow_raise"] = 0.0
            detectList["attention"] = 0.0
            detectList["lip_pucker"] = 0.0
            detectList["lip_suck"] = 0.0
            detectList["mouth_open"] = 0.0
            detectList["eye_closure"] = 0.0
            detectList["pitch"] = 0.0
            detectList["roll"] = 0.0

            actualInterviewViewModel.faceDetectData?.add(detectList)

            Log.d("ActionLog", "onImageResultsSub faceDetectData FACES $detectList")
        } else {
            var values: FloatArray = EmotionValuesManager.GetValues(
                (System.currentTimeMillis() - faceDetectTimeStart).toFloat() / 1000.0f,
                faces[0]
            )

            detectList["disgust"] = values[EmotionValuesManager.VALUEINDEX_DISGUST]
            detectList["sadness"] = values[EmotionValuesManager.VALUEINDEX_SADNESS]
            detectList["engagement"] = values[EmotionValuesManager.VALUEINDEX_ENGAGEMENT]
            detectList["cheek_raise"] = values[EmotionValuesManager.VALUEINDEX_CHEEKRAISE]
            detectList["smile"] = values[EmotionValuesManager.VALUEINDEX_SMILE]
            detectList["smirk"] = values[EmotionValuesManager.VALUEINDEX_SMIRK]
            detectList["brow_furrow"] = values[EmotionValuesManager.VALUEINDEX_BROWFURROW]
            detectList["eye_widen"] = values[EmotionValuesManager.VALUEINDEX_EYEWIDEN]
            detectList["inner_brow_raise"] = values[EmotionValuesManager.VALUEINDEX_INNERBROWRAISE]
            detectList["brow_raise"] = values[EmotionValuesManager.VALUEINDEX_BROWRAISE]
            detectList["attention"] = values[EmotionValuesManager.VALUEINDEX_ATTENTION]
            detectList["lip_pucker"] = values[EmotionValuesManager.VALUEINDEX_LIPPUCKER]
            detectList["lip_suck"] = values[EmotionValuesManager.VALUEINDEX_LIPSUCK]
            detectList["mouth_open"] = values[EmotionValuesManager.VALUEINDEX_MOUTHOPEN]
            detectList["eye_closure"] = values[EmotionValuesManager.VALUEINDEX_EYECLOSURE]
            detectList["pitch"] = values[EmotionValuesManager.VALUEINDEX_PITCH]
            detectList["roll"] = values[EmotionValuesManager.VALUEINDEX_ROLL]

            Log.d("ActionLog", "onImageResultsSub faceDetectData FACES EMPTY $detectList")
        }

        actualInterviewViewModel.faceDetectData?.add(detectList)
    }
}