package jp.co.cac.ope.shokken.lab.utils

import android.content.Context
import android.content.Intent
import android.content.SharedPreferences
import android.content.SharedPreferences.Editor
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.fragment.app.FragmentManager
import com.amazonaws.mobileconnectors.cognitoidentityprovider.CognitoUserSession
import jp.co.cac.ope.shokken.lab.AppConstants
import jp.co.cac.ope.shokken.lab.OPELabApplication
import jp.co.cac.ope.shokken.lab.activity.LoginActivity
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.TimeZone

object LoginLogoutUtil {
    fun checkLoginExpireAndLogout(
        sharedPreferences: SharedPreferences,
        context: Context,
        fragment: Fragment,
        fragmentActivity: FragmentActivity?
    ): Boolean {
        if (SharedPreferenceUtil.isRefreshTokenExpire(sharedPreferences)) {
            safeLogout(sharedPreferences, context, fragment, fragmentActivity)

            return true
        } else {
            return false
        }
    }

    fun safeLogout(
        sharedPreferences: SharedPreferences, context: Context, fragment: Fragment,
        fragmentActivity: FragmentActivity?
    ) {
        deleteLoginInfo(sharedPreferences.edit())
        SharedPreferenceUtil.setNeedLoginFlg(sharedPreferences.edit(), true)

        val intent = Intent(context, LoginActivity::class.java)
        fragment.startActivity(intent)
        fragmentActivity?.finish()
    }

    // ログイン後やセッションリフレッシュ後に保持しておくCognito情報を設定
    fun setLoginTokenInfo(editor: Editor, result: CognitoUserSession) {
        setLoginTokenInfo(
            editor,
            result.idToken.jwtToken,
            result.accessToken.jwtToken,
            result.refreshToken.token
        )
    }

    // ログイン後やセッションリフレッシュ後に保持しておくCognito情報を設定
    fun setLoginTokenInfo(
        editor: Editor,
        idToken: String,
        accessToken: String,
        refreshToken: String
    ) {
        // APIレスポンスからIDTokenを取得し保持
        SharedPreferenceUtil.setIDToken(editor, idToken)
        // APIレスポンスからAccessTokenを取得し保持
        SharedPreferenceUtil.setAccessToken(editor, accessToken)
        // APIレスポンスからRefreshTokenを取得し保持
        SharedPreferenceUtil.setRefreshToken(editor, refreshToken)
        // RefreshTokenの有効期限切れとする日時を保存する
        //フォーマッターの設定
        val formatter = SimpleDateFormat(AppConstants.REFRESH_TOKEN_FORMAT, Locale.JAPAN)
        formatter.timeZone = TimeZone.getTimeZone("Asia/Tokyo")
        val calendar = Calendar.getInstance(Locale.JAPAN)
        calendar.timeZone = TimeZone.getTimeZone("Asia/Tokyo")
        calendar.isLenient = false
        //現在日時を取得
        calendar.time = Date()
        calendar.add(Calendar.SECOND, AppConstants.REFRESH_TOKEN_EXPIRATION)
        //有効期限切れ日時を算出する
        val expirationDate = calendar.time

        //YYYY/MM/dd HH:mm:ssの形でアプリに保持させる
        val expitationDateStr = formatter.format(expirationDate)
        SharedPreferenceUtil.setRefreshTokenExpirationDate(editor, expitationDateStr)
    }

    // ログイン後に必要な情報を設定
    fun setLoginInfo(editor: Editor, subject: String, email: String) {
        // subをUDIDとして保持
        SharedPreferenceUtil.setUDID(editor, subject)
        // emailを保持
        SharedPreferenceUtil.setEMail(editor, email)
    }

    // ログイン後に必要な情報を設定
    fun setLoginFlagInfo(editor: Editor, mode: Int) {
        // ログイン済みの場合はプロフィール登録済み
        //SharedPreferenceUtil.setConfiguredProfile(editor, true)
        // モードを設定
        SharedPreferenceUtil.setMode(editor, mode)

        // ログイン済みフラグを立てる
        SharedPreferenceUtil.setLoggedIn(editor, true)
        // 初期画面でログイン画面は不要
        SharedPreferenceUtil.setNeedLoginFlg(editor, false)
    }

    // セッション切れやログアウト後にアプリ内部的なログイン情報を削除
    fun deleteLoginInfo(editor: Editor) {
        // IDTokenを削除
        SharedPreferenceUtil.setIDToken(editor, "")
        // AccessTokenを削除
        SharedPreferenceUtil.setAccessToken(editor, "")
        // RefreshTokenを削除
        SharedPreferenceUtil.setRefreshToken(editor, "")
        // RefreshTokenの有効期限を削除
        SharedPreferenceUtil.setRefreshTokenExpirationDate(editor, "")

        // UDIDを削除
        SharedPreferenceUtil.setUDID(editor, "")

        //課金済フラグリセット
        SharedPreferenceUtil.setAlreadyBilledFlg(editor, false)

        // emailを削除
        SharedPreferenceUtil.setEMail(editor, "")

        // プロフィール登録フラグをリセット
        //SharedPreferenceUtil.setConfiguredProfile(editor, false)
        // モードをリセット
        SharedPreferenceUtil.setMode(editor, 0)

        // ログイン済みフラグをリセット
        SharedPreferenceUtil.setLoggedIn(editor, false)
        // 初期画面でログインまたは始める画面表示
        SharedPreferenceUtil.setLoginOrStartFlg(editor, true)
    }

    fun showPaidModal(
        from: String,
        isPaidUser: Boolean,
        resId: Int,
        isShowTitleLogin: Boolean,
        messageLogin: String,
        isShowTitlePremium: Boolean,
        messagePremium: String,
        sharedPreferences: SharedPreferences,
        fragmentManager: FragmentManager,
        fragment: Fragment,
        context: Context,
        application: OPELabApplication
    ): Boolean {
        return true
        // 非ログインユーザー：課金誘導モーダル（非ログイン向け）表示
        /*if (!SharedPreferenceUtil.isLoggedIn(sharedPreferences)) {
            PaidDialogFragment.newInstance(
                isShowTitleLogin,
                messageLogin,
                "ログイン/新規会員登録",
                object : PaidDialogFragment.CallbackListener {
                    override fun callbackFromDialogCloseButton() {
                        application.setFromScreen2Login(from)
                        application.setToBillingIntroduction(true)

                        val intent = Intent(context, LoginActivity::class.java)
                        fragment.startActivity(intent)
                    }
                }).show(fragmentManager, "PaidDialogFragment")

            return false
        } else {
            // ログイン無料ユーザー：課金誘導モーダル表示
            if (!isPaidUser) {
                PaidDialogFragment.newInstance(
                    isShowTitlePremium,
                    messagePremium,
                    "Premiumプラン登録",
                    object : PaidDialogFragment.CallbackListener {
                        override fun callbackFromDialogCloseButton() {
                            //  課金導入画面
                            fragment.findNavController().navigate(resId)
                        }
                    }).show(fragmentManager, "PaidDialogFragment")

                return false
            }
            // ログイン有料ユーザー
            else {
                // 今のところ該当するもの無し

                return true
            }
        }*/
    }

    fun checkTrial(
        from: String,
        resId: Int,
        sharedPreferences: SharedPreferences,
        fragmentManager: FragmentManager,
        fragment: Fragment,
        context: Context,
        application: OPELabApplication
    ) {
        // Userdefaultsから１ヶ月間表示しない期間になっているかどうか判定する
        /*if (SharedPreferenceUtil.isTrialDisplayPeriod(sharedPreferences)) {
            //「次回以降表示しない」の有効期間中でなかった場合

            // ユーザー情報取得APIを叩く
            // 通信して取得する
            UserAPI.getDetail(
                sharedPreferences,
                fragmentManager,
                { result: UserModel, error: Exception? ->
                    if (error == null) {
                        // 既に課金済みか判定
                        if (result.billing_flg_android != 1) {
                            //課金済みでなかった場合

                            // ログインユーザーか否かで出しわけ
                            if (SharedPreferenceUtil.isLoggedIn(sharedPreferences)) {
                                TrialDialogFragment.newInstance(
                                    "Premiumプランを体験",
                                    "(初回登録のみ1週間無料)",
                                    object : TrialDialogFragment.CallbackListener {
                                        override fun callbackFromDialogCloseButton(isCheck: Boolean) {
                                            if (isCheck) {
                                                //次回表示開始する日時を保存する
                                                SharedPreferenceUtil.setTrialDisplayPeriod(
                                                    sharedPreferences.edit(),
                                                    "save"
                                                )
                                            }

                                            //  課金導入画面
                                            fragment.findNavController().navigate(resId)
                                        }
                                    }).show(fragmentManager, "TrialDialogFragment")
                            } else {
                                TrialDialogFragment.newInstance(
                                    "ログイン/新規会員登録",
                                    "",
                                    object : TrialDialogFragment.CallbackListener {
                                        override fun callbackFromDialogCloseButton(isCheck: Boolean) {
                                            if (isCheck) {
                                                //次回表示開始する日時を保存する
                                                SharedPreferenceUtil.setTrialDisplayPeriod(
                                                    sharedPreferences.edit(),
                                                    "save"
                                                )
                                            }

                                            application.setFromScreen2Login(from)
                                            application.setToBillingIntroduction(true)

                                            val intent = Intent(context, LoginActivity::class.java)
                                            fragment.startActivity(intent)
                                        }
                                    }).show(fragmentManager, "PaidDialogFragment")
                            }
                        } else if (result.billing_flg_android == 1) {
                            //課金済みだった場合、次回以降通信を省くために、期間に１ヶ月ではなく長い年月を指定する
                            SharedPreferenceUtil.setTrialDisplayPeriod(
                                sharedPreferences.edit(),
                                "never_display"
                            )
                        }
                    }
                })
        }*/
    }

    // Add this method to handle 403 errors
    fun handle403Error(
        sharedPreferences: SharedPreferences,
        context: Context,
        fragment: Fragment,
        fragmentActivity: FragmentActivity?
    ): Boolean {
        // Log the 403 error
        Log.d("API", "Received 403 error, logging out user")
        
        // Perform logout
        safeLogout(sharedPreferences, context, fragment, fragmentActivity)
        
        return true
    }
}
