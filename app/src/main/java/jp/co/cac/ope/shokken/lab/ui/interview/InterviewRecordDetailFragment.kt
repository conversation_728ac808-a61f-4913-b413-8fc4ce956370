package jp.co.cac.ope.shokken.lab.ui.interview

import android.content.Context.MODE_PRIVATE
import android.content.res.Resources
import android.net.Uri
import android.os.Bundle
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.ConstraintSet
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.media3.common.MediaItem
import androidx.media3.common.Player
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.ui.PlayerView
import androidx.navigation.NavOptions
import androidx.navigation.fragment.findNavController
import com.google.android.material.bottomnavigation.BottomNavigationView
import jp.co.cac.ope.shokken.lab.AppConstants
import jp.co.cac.ope.shokken.lab.R
import jp.co.cac.ope.shokken.lab.connects.MockInterviewAPI
import jp.co.cac.ope.shokken.lab.databinding.FragmentInterviewRecordDetailBinding
import jp.co.cac.ope.shokken.lab.model.MockInterviewContentModel
import jp.co.cac.ope.shokken.lab.model.MockInterviewGradeInfoModel
import jp.co.cac.ope.shokken.lab.model.MockInterviewTranscriptionModel
import jp.co.cac.ope.shokken.lab.ui.modal.BottomAnnounceDialogFragment
import jp.co.cac.ope.shokken.lab.ui.modal.CommonDialogFragment
import jp.co.cac.ope.shokken.lab.ui.modal.MemoDialogFragment
import jp.co.cac.ope.shokken.lab.utils.CustomerIOTrainingUtil
import jp.co.cac.ope.shokken.lab.utils.Prop
import java.io.File

class InterviewRecordDetailFragment : Fragment() {
    companion object {
        fun args(
            mockInterviewContent: MockInterviewContentModel,
            mockInterviewGrade: MockInterviewGradeInfoModel,
            mockInterviewTranscriptionModel: MockInterviewTranscriptionModel,
        ): Bundle {
            return Bundle().apply {
                putParcelable(Prop.PRIMARY_ARG, mockInterviewContent)
                putParcelable(Prop.SECONDARY_ARG, mockInterviewGrade)
                putParcelable(Prop.THIRD_ARG, mockInterviewTranscriptionModel)
            }
        }
    }

    private var _binding: FragmentInterviewRecordDetailBinding? = null

    // This property is only valid between onCreateView and
    // onDestroyView.
    private val binding get() = _binding!!

    private lateinit var interviewRecordViewModel: InterviewRecordViewModel

    private lateinit var player: ExoPlayer
    private lateinit var playerView: PlayerView
    private lateinit var fullPlayerView: PlayerView
    private lateinit var noMovie: TextView
    private lateinit var movieFile: File

    private var isExpanded = false

    private var isPlayerPlaying: Boolean = false

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val sharedPreferences =
            requireActivity().getSharedPreferences(AppConstants.APP_PREF_NAME, MODE_PRIVATE)

        val navView: BottomNavigationView = activity?.findViewById(R.id.nav_view)!!
        navView.visibility = View.GONE

        val interviewRecordDetailViewModel =
            ViewModelProvider(this).get(InterviewRecordDetailViewModel::class.java)

        interviewRecordViewModel =
            ViewModelProvider(requireActivity()).get(InterviewRecordViewModel::class.java)

        // Handle when from Home
        val selectInterviewArg =
            arguments?.getParcelable<MockInterviewContentModel>(Prop.PRIMARY_ARG)
        val mockInterviewGradeInfoArg =
            arguments?.getParcelable<MockInterviewGradeInfoModel>(Prop.SECONDARY_ARG)
        val mockInterviewTranscriptionArg =
            arguments?.getParcelable<MockInterviewTranscriptionModel>(Prop.THIRD_ARG)
        if (selectInterviewArg != null) interviewRecordViewModel.selectInterview =
            selectInterviewArg
        if (mockInterviewGradeInfoArg != null) interviewRecordViewModel.mockInterviewGradeInfo =
            mockInterviewGradeInfoArg
        if (mockInterviewTranscriptionArg != null) interviewRecordViewModel.mockInterviewTranscription =
            mockInterviewTranscriptionArg

        _binding = FragmentInterviewRecordDetailBinding.inflate(inflater, container, false)
        val root: View = binding.root

        val titleTextview: TextView = binding.backHeaderBar.headerTitle
        interviewRecordDetailViewModel.titleText.observe(viewLifecycleOwner) {
            titleTextview.text = it
        }

        val backButton: ImageButton = binding.backHeaderBar.backButton
        backButton.setOnClickListener {
            requireActivity().onBackPressedDispatcher.onBackPressed()
        }

        movieFile = File(
            requireContext().getDir(AppConstants.MOVIE_DIR_NAME, MODE_PRIVATE),
            interviewRecordViewModel.mockInterviewGradeInfo?.movie_file_name!!
        )
        Log.d("ActionLog", movieFile.absolutePath.toString())

        if (interviewRecordViewModel.mockInterviewGradeInfo?.movie_file_name!!.isNotEmpty() && movieFile.exists() && movieFile.isFile) {
            Log.d("ActionLog", "Load movie")

            player = ExoPlayer.Builder(requireActivity()).build().apply {
                playWhenReady = false

                // 再生する動画の パス を指定
                val mediaItem = MediaItem.fromUri(Uri.fromFile(movieFile))
                setMediaItem(mediaItem)

                prepare()
            }

            var firstController = true
            player.addListener(object : Player.Listener {
                override fun onPlaybackStateChanged(state: Int) {
                    when (state) {
                        Player.STATE_READY -> {
                            if (firstController) {
                                firstController = false

                                binding.fullMovieLayout.visibility = View.GONE
                                binding.movieLayout.visibility = View.VISIBLE

                                binding.backward.visibility = View.GONE
                                binding.fullBackward.visibility = View.GONE
                                binding.play.visibility = View.VISIBLE
                                binding.fullPlay.visibility = View.VISIBLE
                                binding.replay.visibility = View.GONE
                                binding.fullReplay.visibility = View.GONE
                                binding.pause.visibility = View.GONE
                                binding.fullPause.visibility = View.GONE
                                binding.forward.visibility = View.GONE
                                binding.fullForward.visibility = View.GONE
                            }
                        }

                        Player.STATE_ENDED -> {
                            isPlayerPlaying = false

                            binding.backward.visibility = View.GONE
                            binding.fullBackward.visibility = View.GONE
                            binding.play.visibility = View.GONE
                            binding.fullPlay.visibility = View.GONE
                            binding.replay.visibility = View.VISIBLE
                            binding.fullReplay.visibility = View.VISIBLE
                            binding.pause.visibility = View.GONE
                            binding.fullPause.visibility = View.GONE
                            binding.forward.visibility = View.GONE
                            binding.fullForward.visibility = View.GONE
                        }
                    }
                }
            })

            playerView = PlayerView(requireContext()).apply {
                id = View.generateViewId()
                layoutParams = ConstraintLayout.LayoutParams(
                    ConstraintLayout.LayoutParams.MATCH_PARENT,
                    ConstraintLayout.LayoutParams.WRAP_CONTENT
                )
                visibility = View.GONE
                useController = false
            }
            binding.movieLayout.addView(playerView, 0)

            ConstraintSet().apply {
                clone(binding.movieLayout)

                connect(
                    playerView.id,
                    ConstraintSet.START,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.START
                )
                connect(
                    playerView.id,
                    ConstraintSet.END,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.END
                )

                connect(
                    playerView.id,
                    ConstraintSet.TOP,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.TOP
                )

                connect(
                    playerView.id,
                    ConstraintSet.BOTTOM,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.BOTTOM
                )

                applyTo(binding.movieLayout)
            }

            fullPlayerView = PlayerView(requireContext()).apply {
                id = View.generateViewId()
                layoutParams = ConstraintLayout.LayoutParams(
                    ConstraintLayout.LayoutParams.MATCH_PARENT,
                    ConstraintLayout.LayoutParams.WRAP_CONTENT
                )
                visibility = View.GONE
                useController = false
            }
            binding.fullMovieLayout.addView(fullPlayerView, 0)

            ConstraintSet().apply {
                clone(binding.fullMovieLayout)

                connect(
                    fullPlayerView.id,
                    ConstraintSet.START,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.START
                )
                connect(
                    fullPlayerView.id,
                    ConstraintSet.END,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.END
                )

                connect(
                    fullPlayerView.id,
                    ConstraintSet.TOP,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.TOP
                )

                connect(
                    fullPlayerView.id,
                    ConstraintSet.BOTTOM,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.BOTTOM
                )

                applyTo(binding.fullMovieLayout)
            }

            // control系
            binding.fullsize.setOnClickListener {
                if (!isExpanded) {
                    binding.fullMovieLayout.visibility = View.VISIBLE
                    binding.movieLayout.visibility = View.GONE

                    playerView.player = null
                    playerView.visibility = View.GONE
                    fullPlayerView.player = player
                    fullPlayerView.visibility = View.VISIBLE

                    isExpanded = true
                }
            }
            binding.minisize.setOnClickListener {
                if (isExpanded) {
                    binding.fullMovieLayout.visibility = View.GONE
                    binding.movieLayout.visibility = View.VISIBLE

                    fullPlayerView.player = null
                    fullPlayerView.visibility = View.GONE
                    playerView.player = player
                    playerView.visibility = View.VISIBLE

                    isExpanded = false
                }
            }
            binding.backward.setOnClickListener {
                backward()
            }
            binding.fullBackward.setOnClickListener {
                backward()
            }
            binding.play.setOnClickListener {
                play()
            }
            binding.fullPlay.setOnClickListener {
                play()
            }
            binding.pause.setOnClickListener {
                pause()
            }
            binding.fullPause.setOnClickListener {
                pause()
            }
            binding.replay.setOnClickListener {
                replay()
            }
            binding.fullReplay.setOnClickListener {
                replay()
            }
            binding.forward.setOnClickListener {
                forward()
            }
            binding.fullForward.setOnClickListener {
                forward()
            }

            fullPlayerView.player = null
            fullPlayerView.visibility = View.GONE
            playerView.player = player
            playerView.visibility = View.VISIBLE
        } else {
            Log.d("ActionLog", "Load no movie Image")

            binding.fullsize.visibility = View.GONE
            binding.minisize.visibility = View.GONE
            binding.controlLayout.visibility = View.GONE
            binding.fullControlLayout.visibility = View.GONE

            val movieLayoutParams = ConstraintLayout.LayoutParams(
                ConstraintLayout.LayoutParams.MATCH_PARENT,
                (100 * Resources.getSystem().displayMetrics.density).toInt()
            ).apply {
                startToStart = ConstraintLayout.LayoutParams.PARENT_ID
                endToEnd = ConstraintLayout.LayoutParams.PARENT_ID
                topToBottom = R.id.textView1
                topMargin = (20.0f * Resources.getSystem().displayMetrics.density).toInt()
            }

            binding.movieLayout.layoutParams = movieLayoutParams

            noMovie = TextView(requireContext()).apply {
                id = View.generateViewId()
                layoutParams = ConstraintLayout.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
                )
                gravity = Gravity.CENTER
                visibility = View.GONE
                text = "ファイルが見つかりません"
            }
            binding.movieLayout.addView(noMovie, 0)

            ConstraintSet().apply {
                clone(binding.movieLayout)

                connect(
                    noMovie.id,
                    ConstraintSet.START,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.START
                )
                connect(
                    noMovie.id,
                    ConstraintSet.END,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.END
                )

                connect(
                    noMovie.id,
                    ConstraintSet.TOP,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.TOP
                )

                connect(
                    noMovie.id,
                    ConstraintSet.BOTTOM,
                    ConstraintSet.PARENT_ID,
                    ConstraintSet.BOTTOM
                )

                applyTo(binding.movieLayout)
            }

            noMovie.visibility = View.VISIBLE
        }

        binding.totalScore.text =
            interviewRecordViewModel.mockInterviewGradeInfo?.total_score.toString()

        val sorted =
            interviewRecordViewModel.mockInterviewGradeInfo?.expression_analysis?.sortedBy { it.order }

        val radarLabels = listOf(
            binding.radarLabel1,
            binding.radarLabel2,
            binding.radarLabel3,
            binding.radarLabel4
        )
        val radarLabelScores = listOf(
            binding.radarLabel1Score,
            binding.radarLabel2Score,
            binding.radarLabel3Score,
            binding.radarLabel4Score
        )
        val radarLabelButtons = listOf(
            binding.radarLabel1Button,
            binding.radarLabel2Button,
            binding.radarLabel3Button,
            binding.radarLabel4Button
        )
        if (sorted != null && sorted.size >= 4) {
            // チャート内のグリッド線を破線にできるパラメータがChartsAPIに見つけられなかったので、独自実装
            val radarChart = binding.radarChart

            for ((index, value) in sorted.withIndex()) {
                // 名称
                radarLabels[index].text = value.analysis_item_name

                // スコア
                radarLabelScores[index].text = value.analysis_score.toString()

                // ツールチップ
                radarLabelButtons[index].setOnClickListener {
                    BottomAnnounceDialogFragment.newInstance(
                        value.analysis_item_name + "\n" + value.analysis_description,
                        BottomAnnounceDialogFragment.STYLE_NORMAL,
                        object : BottomAnnounceDialogFragment.CallbackListener {
                            override fun callbackFromDialogCloseButton() {
                            }

                            override fun callbackFromDialogBottomButton(text: String) {
                            }
                        })
                        .show(
                            requireActivity().supportFragmentManager,
                            "BottomAnnounceDialogFragment"
                        )
                }

                radarChart.data.add(value.analysis_score.toDouble())
            }
            radarChart.invalidate()
        }

        binding.expressionGraph.updateDataFromScorePerFrame(interviewRecordViewModel.mockInterviewGradeInfo?.score_per_frame)

        binding.resultFaceText.text =
            interviewRecordViewModel.mockInterviewGradeInfo?.expression_evaluation

        // 項番10, 11, 12
        // ・script_hint_newとscript_hint_changeが両方null(聞くコンテンツ)の場合はトーク分析自体表示しない
        // ・listening_contents_flgが1の時はトーク分析自体を表示しない
        if ((interviewRecordViewModel.selectInterview != null && interviewRecordViewModel.selectInterview?.script_hint_new.isNullOrEmpty() && interviewRecordViewModel.selectInterview?.script_hint_change.isNullOrEmpty()) ||
            (interviewRecordViewModel.mockInterviewGradeInfo?.listening_contents_flg == 1)
        ) {
            binding.textView4.visibility = View.GONE
            binding.analyzeSpeechLayout.visibility = View.GONE
        } else {
            binding.speedHint.setOnClickListener {
                BottomAnnounceDialogFragment.newInstance(
                    "スピード\n撮影時間と音声認識による文字起こしの文字数で算出しています。\n1分間で300~330文字ぐらいが聞きやすい範囲と言われています。\nゲージ内の適切な範囲に収まるように意識して練習しましょう。",
                    BottomAnnounceDialogFragment.STYLE_NORMAL,
                    object : BottomAnnounceDialogFragment.CallbackListener {
                        override fun callbackFromDialogCloseButton() {
                        }

                        override fun callbackFromDialogBottomButton(text: String) {
                        }
                    })
                    .show(requireActivity().supportFragmentManager, "BottomAnnounceDialogFragment")
            }

            val transcriptionBar = binding.transcriptionBar
            transcriptionBar.mockInterviewTranscription =
                interviewRecordViewModel.mockInterviewTranscription
            transcriptionBar.invalidate()

            binding.similarityHint.setOnClickListener {
                BottomAnnounceDialogFragment.newInstance(
                    "台本類似度\n台本入力したテキストと音声認識による文字起こしテキストを比較し、類似度を算出します。\n80％以上で概ね一致していると言えます。\n慣れてきたら台本を見ずに練習もしてみましょう。",
                    BottomAnnounceDialogFragment.STYLE_NORMAL,
                    object : BottomAnnounceDialogFragment.CallbackListener {
                        override fun callbackFromDialogCloseButton() {
                        }

                        override fun callbackFromDialogBottomButton(text: String) {
                        }
                    })
                    .show(requireActivity().supportFragmentManager, "BottomAnnounceDialogFragment")
            }

            val similarityBar = binding.similarityBar
            similarityBar.scriptSimilarity =
                interviewRecordViewModel.mockInterviewTranscription?.script_similarity
            similarityBar.invalidate()

            // 台本類似度がある場合表示
            if (interviewRecordViewModel.mockInterviewTranscription?.script_similarity!!.toInt() >= 0) {
                binding.textView10.visibility = View.GONE
            }
            // ない場合は分析結果なし表示
            else {
                binding.textView10.visibility = View.VISIBLE
            }
        }

        binding.resultSpeechText.text =
            interviewRecordViewModel.mockInterviewTranscription?.transcription

        binding.trainingButton.setOnClickListener {
            // CustomerIOへデータ送信
            CustomerIOTrainingUtil.startedTraining()

            // 保存せずそのまま遷移
            findNavController().navigate(
                R.id.action_navigation_interview_record_detail_to_navigation_training_top,
                null,
                NavOptions.Builder()
                    .setPopUpTo(
                        R.id.navigation_interview_record_top,
                        true
                    )
                    .build()
            )
            val navView: BottomNavigationView = activity?.findViewById(R.id.nav_view)!!
            navView.visibility = View.VISIBLE
        }

        binding.memoButton.setOnClickListener {
            MemoDialogFragment.newInstance(
                interviewRecordViewModel.mockInterviewGradeInfo?.memo!!,
                object : MemoDialogFragment.CallbackListener {
                    override fun callbackFromDialogCloseButton() {
                    }

                    override fun callbackFromDialogSaveButton(newMemo: String) {
                        interviewRecordViewModel.mockInterviewGradeInfo?.memo = newMemo

                        // 通信して模擬面接コンテンツ一覧を編集する
                        MockInterviewAPI.EditMockInterviewGrade(
                            interviewRecordViewModel.mockInterviewGradeInfo?.mock_interview_grade_id!!,
                            interviewRecordViewModel.mockInterviewGradeInfo?.memo!!,
                            sharedPreferences,
                            parentFragmentManager,
                            { result: Boolean, error: Exception? ->
                                if (error != null) {
                                    CommonDialogFragment.newInstance(
                                        "模擬面接結果メモ更新処理でエラーが発生しました。",
                                        true,
                                        "OK",
                                        object : CommonDialogFragment.CallbackListener {
                                            override fun callbackFromDialogCloseButton() {
                                            }

                                            override fun callbackFromDialogCancelButton() {
                                            }
                                        }
                                    ).show(
                                        parentFragmentManager,
                                        "CommonDialogFragment"
                                    )
                                }
                            })
                    }
                })
                .show(requireActivity().supportFragmentManager, "MemoDialogFragment")
        }

        return root
    }

    override fun onPause() {
        Log.i("ActionLog", "onPause IN")

        if (interviewRecordViewModel.mockInterviewGradeInfo?.movie_file_name!!.isNotEmpty() && movieFile.exists() && movieFile.isFile && player.isPlaying) {
            player.pause()
        }

        Log.i("ActionLog", "onPause OUT")

        super.onPause()
    }

    override fun onResume() {
        super.onResume()

        Log.d("ActionLog", "onResume IN")

        if (interviewRecordViewModel.mockInterviewGradeInfo?.movie_file_name!!.isNotEmpty() && movieFile.exists() && movieFile.isFile && isPlayerPlaying) {
            player.play()
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()

        if (interviewRecordViewModel.mockInterviewGradeInfo?.movie_file_name!!.isNotEmpty() && movieFile.exists() && movieFile.isFile) {
            player.release()
        }

        _binding = null
    }

    private fun backward() {
        val currentPosition = player.currentPosition
        val seekPosition = currentPosition - 10000

        player.seekTo(maxOf(seekPosition, 0))
    }

    private fun play() {
        binding.backward.visibility = View.VISIBLE
        binding.fullBackward.visibility = View.VISIBLE
        binding.play.visibility = View.GONE
        binding.fullPlay.visibility = View.GONE
        binding.replay.visibility = View.GONE
        binding.fullReplay.visibility = View.GONE
        binding.pause.visibility = View.VISIBLE
        binding.fullPause.visibility = View.VISIBLE
        binding.forward.visibility = View.VISIBLE
        binding.fullForward.visibility = View.VISIBLE

        player.play()
    }

    private fun pause() {
        binding.backward.visibility = View.GONE
        binding.fullBackward.visibility = View.GONE
        binding.play.visibility = View.VISIBLE
        binding.fullPlay.visibility = View.VISIBLE
        binding.replay.visibility = View.GONE
        binding.fullReplay.visibility = View.GONE
        binding.pause.visibility = View.GONE
        binding.fullPause.visibility = View.GONE
        binding.forward.visibility = View.GONE
        binding.fullForward.visibility = View.GONE

        player.pause()
    }

    private fun replay() {
        binding.backward.visibility = View.VISIBLE
        binding.fullBackward.visibility = View.VISIBLE
        binding.play.visibility = View.GONE
        binding.fullPlay.visibility = View.GONE
        binding.replay.visibility = View.GONE
        binding.fullReplay.visibility = View.GONE
        binding.pause.visibility = View.VISIBLE
        binding.fullPause.visibility = View.VISIBLE
        binding.forward.visibility = View.VISIBLE
        binding.fullForward.visibility = View.VISIBLE

        player.seekTo(0)
        player.play()
    }

    private fun forward() {
        val currentPosition = player.currentPosition
        val seekPosition = currentPosition + 10000
        val duration = player.duration

        player.seekTo(minOf(seekPosition, duration))
    }
}