package jp.co.cac.ope.shokken.lab.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
class ScorePerFrameModel(
    var score_plus: Float = 0.0f,
    var score_minus: Float = 0.0f,
    var timestamp: String = ""
) : Parcelable {

    fun description(): String {
        return "score_plus=$score_plus, score_minus=$score_minus, timestamp=$timestamp"
    }

    companion object {
        fun create(params: Map<String, Any>): ScorePerFrameModel {
            val model = ScorePerFrameModel()

            if (params.containsKey("score_plus") && params["score_plus"] != null) {
                model.score_plus = (params["score_plus"] as Double).toFloat()
            }
            if (params.containsKey("score_minus") && params["score_minus"] != null) {
                // Store the negative value as is - don't convert to positive
                // The ExpressionGraphView will handle the normalization
                model.score_minus = (params["score_minus"] as Double).toFloat()
            }
            if (params.containsKey("timestamp") && params["timestamp"] != null) {
                model.timestamp = params["timestamp"] as String
            }

            return model
        }

        fun createList(paramsList: List<Map<String, Any>>): ArrayList<ScorePerFrameModel> {
            val list = ArrayList<ScorePerFrameModel>()
            paramsList.forEach { params ->
                list.add(create(params))
            }
            return list
        }
    }
}
