package jp.co.cac.ope.shokken.lab.ui.actual_interview

import android.content.Context.MODE_PRIVATE
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.TextView
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import com.google.android.material.bottomnavigation.BottomNavigationView
import jp.co.cac.ope.shokken.lab.AppConstants
import jp.co.cac.ope.shokken.lab.R
import jp.co.cac.ope.shokken.lab.connects.ActualInterviewAPI
import jp.co.cac.ope.shokken.lab.databinding.FragmentActualInterviewRecordDetailBinding
import jp.co.cac.ope.shokken.lab.model.ActualInterviewContentModel
import jp.co.cac.ope.shokken.lab.ui.modal.CommonDialogFragment
import jp.co.cac.ope.shokken.lab.utils.Prop
import jp.co.cac.ope.shokken.lab.utils.SharedPreferenceUtil

class ActualInterviewRecordDetailFragment : Fragment() {
    companion object {
        fun args(
            actualInterviewGradeIdList: ArrayList<Int>,
            selectedGradeId: Int? = null
        ): Bundle {
            return Bundle().apply {
                putIntegerArrayList(Prop.PRIMARY_ARG, actualInterviewGradeIdList)
                if (selectedGradeId != null) putInt(Prop.SECONDARY_ARG, selectedGradeId)
            }
        }
    }

    private var _binding: FragmentActualInterviewRecordDetailBinding? = null

    // This property is only valid between onCreateView and
    // onDestroyView.
    private val binding get() = _binding!!

    private lateinit var actualInterviewRecordViewModel: ActualInterviewRecordViewModel

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        val navView: BottomNavigationView = activity?.findViewById(R.id.nav_view)!!
        navView.visibility = View.GONE

        actualInterviewRecordViewModel =
            ViewModelProvider(requireActivity())[ActualInterviewRecordViewModel::class.java]

        val actualInterviewGradeIdList =
            arguments?.getIntegerArrayList(Prop.PRIMARY_ARG) ?: emptyList()
        val selectedGradeId = arguments?.getInt(Prop.SECONDARY_ARG)

        _binding = FragmentActualInterviewRecordDetailBinding.inflate(inflater, container, false)
        val root: View = binding.root

        val titleTextview: TextView = binding.backHeaderBar.headerTitle
        titleTextview.text = "採点結果"

        val backButton: ImageButton = binding.backHeaderBar.backButton
        backButton.setOnClickListener {
            requireActivity().onBackPressedDispatcher.onBackPressed()
        }

        if (actualInterviewRecordViewModel.actualInterviewContentList.isEmpty()) {
            // This is to handle when from Home, actualInterviewRecordViewModel.actualInterviewContentList would be empty
            // We need to load the list first
            getActualInterviewContentList {
                initViewPager(actualInterviewGradeIdList, selectedGradeId)
            }
        } else {
            initViewPager(actualInterviewGradeIdList, selectedGradeId)
        }

        return root
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    private fun initViewPager(actualInterviewGradeIdList: List<Int>, selectedGradeId: Int? = null) {
        binding.viewPager.apply {
            isUserInputEnabled = false
            offscreenPageLimit = 2
            adapter = ActualInterviewRecordContentPagerAdapter(
                this@ActualInterviewRecordDetailFragment,
                actualInterviewGradeIdList
            )

            if (selectedGradeId != null) {
                setCurrentItem(actualInterviewGradeIdList.indexOf(selectedGradeId), false)
            }
        }
    }

    // -1 = left, 1 = right
    fun navigatePager(direction: Int) {
        val currentPos = binding.viewPager.currentItem
        val totalItem = binding.viewPager.adapter?.itemCount ?: 0
        if (direction == -1) {
            if ((currentPos - 1) >= 0) binding.viewPager.currentItem = currentPos - 1
        } else {
            if ((currentPos + 1) < totalItem) binding.viewPager.currentItem = currentPos + 1
        }
    }

    private fun getActualInterviewContentList(completion: () -> Unit) {
        val sharedPreferences =
            requireActivity().getSharedPreferences(AppConstants.APP_PREF_NAME, MODE_PRIVATE)

        ActualInterviewAPI.GetListActualInterviewContent(
            sharedPreferences,
            parentFragmentManager,
            true,
            { result: ArrayList<ActualInterviewContentModel>, error: Exception? ->
                if (error != null) {
                    CommonDialogFragment.newInstance(
                        "模擬面接コンテンツ一覧の取得でエラーが発生しました。",
                        true,
                        "OK",
                        object : CommonDialogFragment.CallbackListener {
                            override fun callbackFromDialogCloseButton() {
                            }

                            override fun callbackFromDialogCancelButton() {
                            }
                        }
                    ).show(
                        parentFragmentManager,
                        "CommonDialogFragment"
                    )
                } else {
                    result.forEach { item ->
                        // ユーザ設定のモードと同じかすべて表示のコンテンツのみ表示
                        if (item.disp_mode == 0 || item.disp_mode == SharedPreferenceUtil.getMode(
                                sharedPreferences
                            )
                        ) {
                            actualInterviewRecordViewModel.actualInterviewContentList.add(item)
                        }
                    }

                    // ユーザ設定のモードによって並び順を変える
                    if (SharedPreferenceUtil.getMode(sharedPreferences) == 1) {
                        actualInterviewRecordViewModel.actualInterviewContentList.sortBy { it.order_new }
                    } else {
                        actualInterviewRecordViewModel.actualInterviewContentList.sortBy { it.order_change }
                    }

                    requireActivity().runOnUiThread { completion() }
                }
            })
    }
}