package jp.co.cac.ope.shokken.lab.ui.actual_interview

import android.content.Context.MODE_PRIVATE
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.lifecycle.ViewModelProvider
import androidx.navigation.fragment.findNavController
import com.github.mikephil.charting.data.Entry
import com.github.mikephil.charting.data.LineData
import com.github.mikephil.charting.data.LineDataSet
import com.github.mikephil.charting.highlight.Highlight
import com.github.mikephil.charting.listener.OnChartValueSelectedListener
import com.google.android.material.bottomnavigation.BottomNavigationView
import com.google.android.material.card.MaterialCardView
import jp.co.cac.ope.shokken.lab.AppConstants
import jp.co.cac.ope.shokken.lab.R
import jp.co.cac.ope.shokken.lab.connects.ActualInterviewAPI
import jp.co.cac.ope.shokken.lab.databinding.FragmentActualInterviewRecordSelectBinding
import jp.co.cac.ope.shokken.lab.model.ActualInterviewContentModel
import jp.co.cac.ope.shokken.lab.model.ActualInterviewGradeInfoModel
import jp.co.cac.ope.shokken.lab.model.ActualInterviewGradeModel
import jp.co.cac.ope.shokken.lab.ui.modal.ActivityIndicatorDialogFragment
import jp.co.cac.ope.shokken.lab.ui.modal.CommonDialogFragment
import jp.co.cac.ope.shokken.lab.ui.modal.MemoDialogFragment
import jp.co.cac.ope.shokken.lab.utils.DateUtil
import jp.co.cac.ope.shokken.lab.utils.SharedPreferenceUtil
import java.io.File

class ActualInterviewRecordSelectFragment : Fragment() {
    private var _binding: FragmentActualInterviewRecordSelectBinding? = null

    // This property is only valid between onCreateView and
    // onDestroyView.
    private val binding get() = _binding!!

    private lateinit var actualInterviewRecordViewModel: ActualInterviewRecordViewModel

    private var pagerBase: Int = 0

    var actualInterviewGrade: ActualInterviewGradeModel? = null

    private var indicator = ActivityIndicatorDialogFragment.newInstance()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {

        val navView: BottomNavigationView = activity?.findViewById(R.id.nav_view)!!
        navView.visibility = View.VISIBLE

        actualInterviewRecordViewModel =
            ViewModelProvider(requireActivity())[ActualInterviewRecordViewModel::class.java]

        _binding = FragmentActualInterviewRecordSelectBinding.inflate(inflater, container, false)
        val root: View = binding.root

        val titleTextview: TextView = binding.backHeaderBar.headerTitle
        titleTextview.text = "過去成績"

        val backButton: ImageButton = binding.backHeaderBar.backButton
        backButton.setOnClickListener {
            requireActivity().onBackPressedDispatcher.onBackPressed()
        }

        // グラフのプロットを選んだ際に表示
        binding.scoreTitle.visibility = View.INVISIBLE
        binding.score.visibility = View.INVISIBLE
        binding.scoreDate.visibility = View.INVISIBLE

        // グラフ基本設定
        binding.practiceGraph.legend.isEnabled = false
        binding.practiceGraph.setDoubleTapToZoomEnabled(false)
        binding.practiceGraph.setPinchZoom(false)
        binding.practiceGraph.isScaleXEnabled = false
        binding.practiceGraph.isScaleYEnabled = false
        binding.practiceGraph.isHighlightPerTapEnabled = true
        binding.practiceGraph.isHighlightPerDragEnabled = true
        binding.practiceGraph.description.isEnabled = false

        // グラフ X軸
        val xAxis = binding.practiceGraph.xAxis
        xAxis.setLabelCount(10, false)
        xAxis.setDrawLabels(false)
        xAxis.setDrawAxisLine(false)
        xAxis.setDrawGridLines(false)

        // 右軸
        val rightAxis = binding.practiceGraph.axisRight
        rightAxis.axisMaximum = 100.0f
        rightAxis.axisMinimum = 0.0f
        rightAxis.setLabelCount(5, true)
        rightAxis.textColor = ContextCompat.getColor(requireContext(), R.color.gray_icon)
        rightAxis.gridLineWidth = 1.0f
        rightAxis.gridColor = ContextCompat.getColor(requireContext(), R.color.base_text)
        rightAxis.enableGridDashedLine(4.0f, 4.0f, 0.0f)
        rightAxis.setDrawAxisLine(false)

        // 左軸無効
        val leftAxis = binding.practiceGraph.axisLeft
        leftAxis.isEnabled = false
        leftAxis.axisMaximum = 100.0f
        leftAxis.axisMinimum = 0.0f
        leftAxis.setLabelCount(5, true)

        binding.practiceGraph.setOnChartValueSelectedListener(object :
            OnChartValueSelectedListener {
            override fun onValueSelected(e: Entry?, h: Highlight?) {
                if (e != null && h != null) {
                    // データセット取得
                    val dataSet = binding.practiceGraph.data?.getDataSetByIndex(h.dataSetIndex)
                    val sliceIndex = dataSet?.getEntryIndex(e) ?: -1

                    val gradeList =
                        actualInterviewRecordViewModel.actualInterviewGrade?.getGradeList()
                            ?: emptyList()

                    if (sliceIndex >= 0) {
                        // 必要なデータ取得
                        val score =
                            gradeList[pagerBase + sliceIndex].total_score
                        val datetime =
                            gradeList[pagerBase + sliceIndex].scored_datetime

                        // ラベル更新
                        binding.score.text = "$score"
                        binding.scoreDate.text = DateUtil.date2List2(datetime)

                        binding.scoreTitle.visibility = View.VISIBLE
                        binding.score.visibility = View.VISIBLE
                        binding.scoreDate.visibility = View.VISIBLE
                    } else {
                        // データセットが無い場合
                        binding.scoreTitle.visibility = View.INVISIBLE
                        binding.score.visibility = View.INVISIBLE
                        binding.scoreDate.visibility = View.INVISIBLE
                    }
                }
            }

            override fun onNothingSelected() {
                // 選択解除時
                binding.scoreTitle.visibility = View.INVISIBLE
                binding.score.visibility = View.INVISIBLE
                binding.scoreDate.visibility = View.INVISIBLE
            }
        })

        binding.prev.visibility = View.INVISIBLE
        binding.prevLabel.visibility = View.INVISIBLE

        binding.prev.setOnClickListener {
            prev()
        }
        binding.prevLabel.setOnClickListener {
            prev()
        }

        binding.next.visibility = View.INVISIBLE
        binding.nextLabel.visibility = View.INVISIBLE
        binding.next.setOnClickListener {
            next()
        }
        binding.nextLabel.setOnClickListener {
            next()
        }

        indicator.show(parentFragmentManager, "ActivityIndicatorDialogFragment")
        getActualInterviewContentList {
            getActualInterviewGradeList {
                indicator.dismissWithAnimation()

                val selectInterview =
                    actualInterviewRecordViewModel.actualInterviewContentList.firstOrNull()
                actualInterviewRecordViewModel.actualInterviewGrade = actualInterviewGrade

                binding.bestScore.text = actualInterviewGrade?.best_score.toString()

                binding.kindLabel.text =
                    selectInterview?.practice_index_name

                binding.totalPracticeCount.text =
                    actualInterviewGrade?.getGradeList()?.count().toString()

                showActualInterviewGradeList()
            }
        }

        return root
    }

    private fun getActualInterviewGradeList(completion: () -> Unit) {
        val sharedPreferences =
            requireActivity().getSharedPreferences(AppConstants.APP_PREF_NAME, MODE_PRIVATE)

        // 通信して模擬面接コンテンツ一覧を取得する
        ActualInterviewAPI.GetActualInterviewGrade(
            sharedPreferences,
            parentFragmentManager,
            false,
            { result: ActualInterviewGradeModel, error: Exception? ->
                if (error != null) {
                    indicator.dismissWithAnimation()
                    CommonDialogFragment.newInstance(
                        "模擬面接コンテンツ一覧の取得でエラーが発生しました。",
                        true,
                        "OK",
                        object : CommonDialogFragment.CallbackListener {
                            override fun callbackFromDialogCloseButton() {}
                            override fun callbackFromDialogCancelButton() {}
                        }
                    ).show(
                        parentFragmentManager,
                        "CommonDialogFragment"
                    )
                } else {
                    Log.d(
                        "Actionlog",
                        "functionName: ActualInterviewAPI.GetActualInterviewGrade, message: 模擬面接結果: ${result.description()}"
                    )

                    actualInterviewGrade = result

                    requireActivity().runOnUiThread { completion() }
                }
            })
    }

    private fun getActualInterviewContentList(completion: () -> Unit) {
        val sharedPreferences =
            requireActivity().getSharedPreferences(AppConstants.APP_PREF_NAME, MODE_PRIVATE)

        ActualInterviewAPI.GetListActualInterviewContent(
            sharedPreferences,
            parentFragmentManager,
            false,
            { result: ArrayList<ActualInterviewContentModel>, error: Exception? ->
                if (error != null) {
                    indicator.dismissWithAnimation()
                    CommonDialogFragment.newInstance(
                        "模擬面接コンテンツ一覧の取得でエラーが発生しました。",
                        true,
                        "OK",
                        object : CommonDialogFragment.CallbackListener {
                            override fun callbackFromDialogCloseButton() {
                            }

                            override fun callbackFromDialogCancelButton() {
                            }
                        }
                    ).show(
                        parentFragmentManager,
                        "CommonDialogFragment"
                    )
                } else {
                    result.forEach { item ->
                        // ユーザ設定のモードと同じかすべて表示のコンテンツのみ表示
                        if (item.disp_mode == 0 || item.disp_mode == SharedPreferenceUtil.getMode(
                                sharedPreferences
                            )
                        ) {
                            actualInterviewRecordViewModel.actualInterviewContentList.add(item)
                        }
                    }

                    // ユーザ設定のモードによって並び順を変える
                    if (SharedPreferenceUtil.getMode(sharedPreferences) == 1) {
                        actualInterviewRecordViewModel.actualInterviewContentList.sortBy { it.order_new }
                    } else {
                        actualInterviewRecordViewModel.actualInterviewContentList.sortBy { it.order_change }
                    }

                    requireActivity().runOnUiThread { completion() }
                }
            })
    }

    private fun showActualInterviewGradeList() {
        val sharedPreferences =
            requireActivity().getSharedPreferences(AppConstants.APP_PREF_NAME, MODE_PRIVATE)

        pagerBase = 0
        resetGraphData()

        // 履歴を追加する
        val contentsContainer: LinearLayout = binding.contentsContainer
        contentsContainer.removeAllViews()

        val gradeList = actualInterviewRecordViewModel.actualInterviewGrade?.getGradeList()!!
        val gradeIdList = gradeList.map { it.actual_interview_grade_id }
        gradeList.forEachIndexed { index, item ->
            val menuButtonView: View = layoutInflater.inflate(
                R.layout.interview_practice_record_menu_button,
                contentsContainer,
                false
            )

            // Score部分を設定
            val scoreTextView: TextView =
                menuButtonView.findViewById(R.id.score)
            scoreTextView.text = item.total_score.toString()

            val scoreDateTextView: TextView = menuButtonView.findViewById(R.id.score_date)
            scoreDateTextView.text = DateUtil.date2List2(item.scored_datetime)

            val card: MaterialCardView = menuButtonView.findViewById(R.id.card)
            // タップで次へ
            card.setOnClickListener {
                Handler(Looper.getMainLooper()).post {
                    findNavController().navigate(
                        R.id.action_navigation_actual_interview_record_select_to_navigation_actual_interview_record_detail,
                        ActualInterviewRecordDetailFragment.args(
                            ArrayList(gradeIdList),
                            item.actual_interview_grade_id
                        )
                    )
                }
            }

            val memo: ImageButton = menuButtonView.findViewById(R.id.memo)
            // メモ編集
            memo.setOnClickListener {
                MemoDialogFragment.newInstance(
                    item.memo,
                    object : MemoDialogFragment.CallbackListener {
                        override fun callbackFromDialogCloseButton() {
                            Log.d("ActionLog", "メモ閉じる")
                        }

                        override fun callbackFromDialogSaveButton(newMemo: String) {
                            Log.d("ActionLog", "メモ保存")
                            item.actual_interview_grade_id
                            item.memo = newMemo

                            // 通信して模擬面接コンテンツ一覧を編集する
                            ActualInterviewAPI.EditActualInterviewGrade(
                                item.actual_interview_grade_id,
                                item.memo,
                                sharedPreferences,
                                parentFragmentManager,
                                { result: Boolean, error: Exception? ->
                                    if (error != null) {
                                        CommonDialogFragment.newInstance(
                                            "模擬面接結果メモ更新処理でエラーが発生しました。",
                                            true,
                                            "OK",
                                            object : CommonDialogFragment.CallbackListener {
                                                override fun callbackFromDialogCloseButton() {
                                                }

                                                override fun callbackFromDialogCancelButton() {
                                                }
                                            }
                                        ).show(
                                            parentFragmentManager,
                                            "CommonDialogFragment"
                                        )
                                    }
                                })
                        }
                    })
                    .show(requireActivity().supportFragmentManager, "MemoDialogFragment")
            }

            val delete: ImageButton = menuButtonView.findViewById(R.id.delete)
            // 履歴削除
            delete.setOnClickListener {
                CommonDialogFragment.newInstance(
                    "削除しますか？",
                    true,
                    "削除する",
                    object : CommonDialogFragment.CallbackListener {
                        override fun callbackFromDialogCloseButton() {
                            // 通信して模擬面接コンテンツ一覧を取得する
                            ActualInterviewAPI.DeleteActualInterviewGrade(
                                item.actual_interview_grade_id,
                                sharedPreferences,
                                parentFragmentManager,
                                { result: Boolean, error: Exception? ->
                                    if (error != null) {
                                        CommonDialogFragment.newInstance(
                                            "模擬面接コンテンツ一覧の取得でエラーが発生しました。",
                                            true,
                                            "OK",
                                            object : CommonDialogFragment.CallbackListener {
                                                override fun callbackFromDialogCloseButton() {
                                                }

                                                override fun callbackFromDialogCancelButton() {
                                                }
                                            }
                                        ).show(
                                            parentFragmentManager,
                                            "CommonDialogFragment"
                                        )
                                    } else {
                                        // 動画ファイルは削除
                                        val movieFile: File = File(
                                            requireContext().getDir(
                                                AppConstants.MOVIE_DIR_NAME,
                                                MODE_PRIVATE
                                            ), item.movie_file_name
                                        )
                                        if (!item.movie_file_name.isNotEmpty() && movieFile.exists() && movieFile.isFile) {
                                            movieFile.delete()
                                        }

                                        // 保持リストのis_deletedをたてる
                                        item.is_deleted =
                                            ActualInterviewGradeInfoModel.IS_DELETED_DELETED

                                        Handler(Looper.getMainLooper()).post {
                                            showActualInterviewGradeList()
                                        }
                                    }
                                })
                        }

                        override fun callbackFromDialogCancelButton() {
                        }
                    },
                    true,
                    "キャンセル"
                ).show(
                    parentFragmentManager,
                    "CommonDialogFragment"
                )
            }

            // コンテナに追加
            contentsContainer.addView(menuButtonView)
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }

    private fun resetGraphData() {
        val gradeList = actualInterviewRecordViewModel.actualInterviewGrade?.getGradeList()!!
        if (pagerBase + 10 < gradeList.count()) {
            binding.next.visibility = View.VISIBLE
            binding.nextLabel.visibility = View.VISIBLE
        } else {
            binding.next.visibility = View.INVISIBLE
            binding.nextLabel.visibility = View.INVISIBLE
        }

        if (0 <= pagerBase - 10) {
            binding.prev.visibility = View.VISIBLE
            binding.prevLabel.visibility = View.VISIBLE
        } else {
            binding.prev.visibility = View.INVISIBLE
            binding.prevLabel.visibility = View.INVISIBLE
        }

        // データクリア
        // 遷移前に選択していたハイライトも消える
        binding.practiceGraph.clear()

        // 次が10件以下の場合を考慮
        var diff = 10
        if (pagerBase + diff > gradeList.count()) {
            diff =
                gradeList.count() - pagerBase
        }

        // データ作成
        val entries = ArrayList<Entry>()
        for (i in pagerBase until pagerBase + diff) {
            val score =
                gradeList[i].total_score.toFloat()
            entries.add(Entry(i.toFloat(), score))
        }

        // データセット
        val dataSet = LineDataSet(entries, "").apply {
            lineWidth = 1.0f
            color = ContextCompat.getColor(
                requireContext(),
                R.color.main_enji
            )
            circleRadius = 3.0f
            setCircleColor(ContextCompat.getColor(requireContext(), R.color.main_enji))
            setDrawHorizontalHighlightIndicator(false)
            highlightLineWidth = 2.0f
            highLightColor = ContextCompat.getColor(requireContext(), R.color.base_text)
            enableDashedHighlightLine(4.0f, 4.0f, 0.0f)
            setDrawFilled(true)
            fillColor = ContextCompat.getColor(requireContext(), R.color.main_enji)
            fillAlpha = 25
            setDrawValues(false)
        }

        // データセットをChartにセット
        binding.practiceGraph.data = LineData(dataSet)
        binding.practiceGraph.invalidate()
    }

    private fun prev() {
        if (pagerBase - 10 >= 0) {
            pagerBase -= 10

            resetGraphData()
        }
    }

    private fun next() {
        if (pagerBase + 10 < actualInterviewRecordViewModel.actualInterviewGrade?.getGradeList()!!
                .count()
        ) {
            pagerBase += 10

            resetGraphData()
        }
    }
}