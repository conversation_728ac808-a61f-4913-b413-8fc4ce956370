package jp.co.cac.ope.shokken.lab.model

import android.os.Parcelable
import kotlinx.parcelize.Parcelize

@Parcelize
class ActualInterviewGradeInfoModel(
    var actual_interview_grade_id: Int = -1,
    var user_id: Int = -1,
    var actual_interview_contents_id: Int = -1,
    var total_score: Int = 0,
    var scored_datetime: String = "",
    var expression_analysis: ArrayList<ExpressionAnalysisInfoModel>? = null,
    var expression_evaluation: String = "",
    var score_per_frame: ArrayList<ScorePerFrameModel>? = null,
    var listening_contents_flg: Int = 0,
    var memo: String = "",
    var movie_file_name: String = "",
    var created_at: String = "",
    var updated_at: String = "",
    var is_deleted: Int = IS_DELETED_NOT_DELETED,
    var deleted_at: String = ""
) : Parcelable {

    fun description(): String {
        return "actual_interview_grade_id=$actual_interview_grade_id, user_id=$user_id, actual_interview_contents_id=$actual_interview_contents_id, " +
                "total_score=$total_score, scored_datetime=$scored_datetime, expression_analysis=$expression_analysis, " +
                "expression_evaluation=$expression_evaluation, score_per_frame=$score_per_frame, " +
                "listening_contents_flg=$listening_contents_flg, memo=$memo, " +
                "movie_file_name=$movie_file_name, created_at=$created_at, updated_at=$updated_at, is_deleted=$is_deleted, deleted_at=$deleted_at"
    }

    fun createRegisterParam(): Map<String, Any> {
        var params: MutableMap<String, Any> = mutableMapOf<String, Any>()

        params["actual_interview_contents_id"] = actual_interview_contents_id
        params["total_score"] = total_score
        params["scored_datetime"] = scored_datetime

        var expressions: ArrayList<Map<String, Any>> = arrayListOf<Map<String, Any>>()
        expression_analysis?.forEach { item ->
            var exp: MutableMap<String, Any> = mutableMapOf<String, Any>()

            exp["analysis_item_id"] = item.analysis_item_id
            exp["analysis_item_name"] = item.analysis_item_name
            exp["analysis_score"] = item.analysis_score
            exp["analysis_description"] = item.analysis_description
            exp["order"] = item.order

            expressions.add(exp)
        }
        params["expression_analysis"] = expressions

        params["expression_evaluation"] = expression_evaluation

        // Add score_per_frame data if available
        if (score_per_frame != null && score_per_frame!!.isNotEmpty()) {
            var scoreFrames: ArrayList<Map<String, Any>> = arrayListOf<Map<String, Any>>()
            score_per_frame!!.forEach { item ->
                var frame: MutableMap<String, Any> = mutableMapOf<String, Any>()
                frame["score_plus"] = item.score_plus
                frame["score_minus"] = -item.score_minus // Convert back to negative for API
                frame["timestamp"] = item.timestamp
                scoreFrames.add(frame)
            }
            params["score_per_frame"] = scoreFrames
        }

        params["listening_contents_flg"] = listening_contents_flg
        params["memo"] = memo
        params["movie_file_name"] = movie_file_name

        return params
    }

    companion object {
        const val IS_DELETED_NOT_DELETED = 0
        const val IS_DELETED_DELETED = 1

        fun createSimple(params: Map<String, Any>): ActualInterviewGradeInfoModel {
            var model = ActualInterviewGradeInfoModel()

            if (params.containsKey("actual_interview_grade_id")
                && params["actual_interview_grade_id"] != null
                && (params["actual_interview_grade_id"] as Double).toInt() >= 0
            ) {
                model.actual_interview_grade_id =
                    (params["actual_interview_grade_id"] as Double).toInt()
            }
            model.user_id = -1
            model.actual_interview_contents_id = -1
            if (params.containsKey("total_score")
                && params["total_score"] != null
                && (params["total_score"] as Double).toInt() >= 0
            ) {
                model.total_score = (params["total_score"] as Double).toInt()
            }
            if (params.containsKey("scored_datetime")
                && params["scored_datetime"] != null
            ) {
                model.scored_datetime = params["scored_datetime"] as String
            }
            model.expression_analysis = arrayListOf<ExpressionAnalysisInfoModel>()
            model.expression_evaluation = ""

            // Parse score_per_frame if available
            if (params.containsKey("score_per_frame") && params["score_per_frame"] != null) {
                val scoreFramesList = params["score_per_frame"] as? List<Map<String, Any>>
                if (scoreFramesList != null) {
                    model.score_per_frame = ScorePerFrameModel.createList(scoreFramesList)
                } else {
                    model.score_per_frame = arrayListOf()
                }
            } else {
                model.score_per_frame = arrayListOf()
            }

            if (params.containsKey("listening_contents_flg")
                && params["listening_contents_flg"] != null
                && (params["listening_contents_flg"] as Double).toInt() >= 0
            ) {
                model.listening_contents_flg = (params["listening_contents_flg"] as Double).toInt()
            }
            if (params.containsKey("memo")
                && params["memo"] != null
            ) {
                model.memo = params["memo"] as String
            }
            if (params.containsKey("movie_file_name")
                && params["movie_file_name"] != null
            ) {
                model.movie_file_name = params["movie_file_name"] as String
            }
            if (params.containsKey("created_at")
                && params["created_at"] != null
            ) {
                model.created_at = params["created_at"] as String
            }
            if (params.containsKey("updated_at")
                && params["updated_at"] != null
            ) {
                model.updated_at = params["updated_at"] as String
            }
            if (params.containsKey("is_deleted")
                && params["is_deleted"] != null
                && (params["is_deleted"] as Double).toInt() >= 0
            ) {
                model.is_deleted = (params["is_deleted"] as Double).toInt()
            }
            if (params.containsKey("deleted_at")
                && params["deleted_at"] != null
            ) {
                model.deleted_at = params["deleted_at"] as String
            }

            return model
        }

        fun createSimple(
            params: Map<String, Any>,
            expression: ArrayList<ExpressionAnalysisInfoModel>
        ): ActualInterviewGradeInfoModel {
            var model = ActualInterviewGradeInfoModel()

            model.actual_interview_grade_id = -1
            model.user_id = -1
            model.actual_interview_contents_id = -1
            if (params.containsKey("total_score")
                && params["total_score"] != null
                && (params["total_score"] as Double).toInt() >= 0
            ) {
                model.total_score = (params["total_score"] as Double).toInt()
            }
            if (params.containsKey("scored_datetime")
                && params["scored_datetime"] != null
            ) {
                model.scored_datetime = params["scored_datetime"] as String
            }
            model.expression_analysis = expression
            if (params.containsKey("expression_evaluation")
                && params["expression_evaluation"] != null
            ) {
                model.expression_evaluation = params["expression_evaluation"] as String
            }

            // Parse score_per_frame if available
            if (params.containsKey("score_per_frame") && params["score_per_frame"] != null) {
                val scoreFramesList = params["score_per_frame"] as? List<Map<String, Any>>
                if (scoreFramesList != null) {
                    model.score_per_frame = ScorePerFrameModel.createList(scoreFramesList)
                } else {
                    model.score_per_frame = arrayListOf()
                }
            } else {
                model.score_per_frame = arrayListOf()
            }

            if (params.containsKey("listening_contents_flg")
                && params["listening_contents_flg"] != null
                && (params["listening_contents_flg"] as Double).toInt() >= 0
            ) {
                model.listening_contents_flg = (params["listening_contents_flg"] as Double).toInt()
            }
            model.memo = ""
            model.movie_file_name = ""
            model.created_at = ""
            model.updated_at = ""
            if (params.containsKey("is_deleted")
                && params["is_deleted"] != null
                && (params["is_deleted"] as Double).toInt() >= 0
            ) {
                model.is_deleted = (params["is_deleted"] as Double).toInt()
            }
            model.deleted_at = ""

            return model
        }

        fun create(
            params: Map<String, Any>,
            expression: ArrayList<ExpressionAnalysisInfoModel>
        ): ActualInterviewGradeInfoModel {
            var model = ActualInterviewGradeInfoModel()

            if (params.containsKey("actual_interview_grade_id")
                && params["actual_interview_grade_id"] != null
                && (params["actual_interview_grade_id"] as Double).toInt() >= 0
            ) {
                model.actual_interview_grade_id =
                    (params["actual_interview_grade_id"] as Double).toInt()
            }
            if (params.containsKey("user_id")
                && params["user_id"] != null
                && (params["user_id"] as Double).toInt() >= 0
            ) {
                model.user_id =
                    (params["user_id"] as Double).toInt()
            }
            if (params.containsKey("actual_interview_contents_id")
                && params["actual_interview_contents_id"] != null
                && (params["actual_interview_contents_id"] as Double).toInt() >= 0
            ) {
                model.actual_interview_contents_id =
                    (params["actual_interview_contents_id"] as Double).toInt()
            }
            if (params.containsKey("total_score")
                && params["total_score"] != null
                && (params["total_score"] as Double).toInt() >= 0
            ) {
                model.total_score = (params["total_score"] as Double).toInt()
            }
            if (params.containsKey("scored_datetime")
                && params["scored_datetime"] != null
            ) {
                model.scored_datetime = params["scored_datetime"] as String
            }
            model.expression_analysis = expression
            if (params.containsKey("expression_evaluation")
                && params["expression_evaluation"] != null
            ) {
                model.expression_evaluation = params["expression_evaluation"] as String
            }

            // Parse score_per_frame if available
            if (params.containsKey("score_per_frame") && params["score_per_frame"] != null) {
                val scoreFramesList = params["score_per_frame"] as? List<Map<String, Any>>
                if (scoreFramesList != null) {
                    model.score_per_frame = ScorePerFrameModel.createList(scoreFramesList)
                } else {
                    model.score_per_frame = arrayListOf()
                }
            } else {
                model.score_per_frame = arrayListOf()
            }

            if (params.containsKey("listening_contents_flg")
                && params["listening_contents_flg"] != null
                && (params["listening_contents_flg"] as Double).toInt() >= 0
            ) {
                model.listening_contents_flg = (params["listening_contents_flg"] as Double).toInt()
            }
            if (params.containsKey("memo")
                && params["memo"] != null
            ) {
                model.memo = params["memo"] as String
            }
            if (params.containsKey("movie_file_name")
                && params["movie_file_name"] != null
            ) {
                model.movie_file_name = params["movie_file_name"] as String
            }
            if (params.containsKey("created_at")
                && params["created_at"] != null
            ) {
                model.created_at = params["created_at"] as String
            }
            if (params.containsKey("updated_at")
                && params["updated_at"] != null
            ) {
                model.updated_at = params["updated_at"] as String
            }
            if (params.containsKey("is_deleted")
                && params["is_deleted"] != null
                && (params["is_deleted"] as Double).toInt() >= 0
            ) {
                model.is_deleted = (params["is_deleted"] as Double).toInt()
            }
            if (params.containsKey("deleted_at")
                && params["deleted_at"] != null
            ) {
                model.deleted_at = params["deleted_at"] as String
            }

            return model
        }
    }
}
